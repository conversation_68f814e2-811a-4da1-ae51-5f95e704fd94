model Testimonial {
  id          BigInt   @id @default(autoincrement())
  name        String   @db.VarChar(255)
  designation String   @db.VarChar(255)
  description String?  @db.Text
  photo       String?  @db.VarChar(255)
  bg_color    String?  @db.VarChar(255)
  status      String   @default("ACTIVE") // ACTIVE, INACTIVE
  created_at  DateTime @default(now())
  updated_at  DateTime @default(now()) @updatedAt

  @@map("testimonials")
}
