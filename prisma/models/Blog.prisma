model Blog {
  id          BigInt   @id @default(autoincrement())
  user_id     BigInt
  title        String
  image       String?
  description String?   @db.Text
  status      String   @default("ACTIVE") // ACTIVE, FROZEN
  created_at  DateTime @default(now())
  updated_at  DateTime @default(now()) @updatedAt
  // Relations
  user User @relation(fields: [user_id], references: [id])

  @@map("blogs")
}
