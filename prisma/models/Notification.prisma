model Notification {
  id         BigInt   @id @default(autoincrement())
  text       String   @db.Text
  user_id    BigInt?  // Nullable - if null, notification is for all users
  is_read    <PERSON><PERSON><PERSON>  @default(false)
  created_at DateTime @default(now())
  updated_at DateTime @default(now()) @updatedAt

  // Relations
  user User? @relation(fields: [user_id], references: [id])

  @@map("notifications")
}
