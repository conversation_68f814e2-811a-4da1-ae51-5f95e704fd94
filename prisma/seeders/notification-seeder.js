const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

async function seedNotifications() {
  console.log("Seeding notifications...");

  // Get some users for user-specific notifications
  const users = await prisma.user.findMany({
    take: 3,
    orderBy: { id: "asc" },
  });

  const notifications = [
    // Global notifications (user_id = null)
    {
      text: "Welcome to our platform! We're excited to have you on board.",
      user_id: null,
      is_read: false,
    },
    {
      text: "System maintenance scheduled for tonight from 2:00 AM to 4:00 AM UTC.",
      user_id: null,
      is_read: false,
    },
    {
      text: "New features have been added to your dashboard. Check them out!",
      user_id: null,
      is_read: false,
    },
    {
      text: "Security update: Please review your account settings and enable two-factor authentication.",
      user_id: null,
      is_read: false,
    },
    {
      text: "Holiday notice: Our support team will have limited availability during the holidays.",
      user_id: null,
      is_read: true,
    },
  ];

  // Add user-specific notifications if users exist
  if (users.length > 0) {
    // Notifications for first user
    notifications.push(
      {
        text: "Your account verification has been completed successfully.",
        user_id: users[0].id,
        is_read: false,
      },
      {
        text: "You have a new transaction pending approval.",
        user_id: users[0].id,
        is_read: false,
      },
      {
        text: "Your withdrawal request has been processed.",
        user_id: users[0].id,
        is_read: true,
      }
    );

    // Notifications for second user if exists
    if (users.length > 1) {
      notifications.push(
        {
          text: "Your profile information has been updated.",
          user_id: users[1].id,
          is_read: false,
        },
        {
          text: "New network address has been added to your account.",
          user_id: users[1].id,
          is_read: true,
        }
      );
    }

    // Notifications for third user if exists
    if (users.length > 2) {
      notifications.push(
        {
          text: "Your account balance has been updated.",
          user_id: users[2].id,
          is_read: false,
        },
        {
          text: "Password change confirmation: Your password has been updated successfully.",
          user_id: users[2].id,
          is_read: true,
        }
      );
    }
  }

  // Check if notifications already exist
  const existingNotifications = await prisma.notification.findMany();
  
  if (existingNotifications.length > 0) {
    console.log("Notifications already exist, skipping seeding.");
    return;
  }

  // Create notifications
  for (const notificationData of notifications) {
    await prisma.notification.create({
      data: {
        text: notificationData.text,
        user_id: notificationData.user_id,
        is_read: notificationData.is_read,
      },
    });
  }

  console.log(`Created ${notifications.length} notifications.`);
  
  // Show summary
  const globalCount = notifications.filter(n => n.user_id === null).length;
  const userSpecificCount = notifications.filter(n => n.user_id !== null).length;
  const unreadCount = notifications.filter(n => !n.is_read).length;
  
  console.log(`- Global notifications: ${globalCount}`);
  console.log(`- User-specific notifications: ${userSpecificCount}`);
  console.log(`- Unread notifications: ${unreadCount}`);
  console.log("Notifications seeded successfully.");
}

module.exports = { seedNotifications };
