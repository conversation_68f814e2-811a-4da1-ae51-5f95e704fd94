const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

async function seedTestimonials() {
  console.log("Seeding testimonials...");

  const testimonials = [
    {
      name: "<PERSON>",
      designation: "CEO, Tech Solutions Inc.",
      description: "This platform has revolutionized how we handle our financial transactions. The security and ease of use are unmatched. Highly recommended for any business looking to streamline their payment processes.",
      photo: "/uploads/sample-avatar-1.jpg",
      bg_color: "#f8f9fa",
      status: "ACTIVE",
    },
    {
      name: "<PERSON>",
      designation: "Financial Director, Global Enterprises",
      description: "Outstanding service and support. The team is always responsive and the platform is incredibly reliable. We've been using it for over a year now and couldn't be happier.",
      photo: "/uploads/sample-avatar-2.jpg",
      bg_color: "#e3f2fd",
      status: "ACTIVE",
    },
    {
      name: "<PERSON>",
      designation: "Founder, StartupXYZ",
      description: "As a startup, we needed a cost-effective solution that could scale with us. This platform delivered exactly that. The transaction fees are competitive and the features are comprehensive.",
      photo: "/uploads/sample-avatar-3.jpg",
      bg_color: "#f3e5f5",
      status: "ACTIVE",
    },
    {
      name: "<PERSON>",
      designation: "Operations Manager, E-commerce Plus",
      description: "The integration was seamless and the documentation is excellent. Our developers had no issues implementing the API. The real-time transaction monitoring is a game-changer.",
      photo: "/uploads/sample-avatar-4.jpg",
      bg_color: "#e8f5e8",
      status: "ACTIVE",
    },
    {
      name: "David Wilson",
      designation: "CTO, FinanceFlow",
      description: "Security is our top priority, and this platform exceeds all our requirements. The multi-layer authentication and encryption give us complete peace of mind.",
      photo: "/uploads/sample-avatar-5.jpg",
      bg_color: "#fff3e0",
      status: "ACTIVE",
    },
  ];

  // Check if testimonials already exist
  const existingTestimonials = await prisma.testimonial.findMany();

  if (existingTestimonials.length > 0) {
    console.log("Testimonials already exist, skipping seeding.");
    return;
  }

  // Create testimonials
  for (const testimonialData of testimonials) {
    await prisma.testimonial.create({
      data: testimonialData,
    });
  }

  console.log(`Created ${testimonials.length} testimonials.`);
  console.log("Testimonials seeded successfully.");
}

module.exports = { seedTestimonials };
