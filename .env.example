APP_NAME=noname
NODE_ENV=development
PORT=8000
JWT_SECRET='secret-key'
JWT_EXPIRES_IN=1y
JWT_COOKIE_EXPIRES_IN=7d
JWT_COOKIE_NAME='jwt'
# This was inserted by `prisma init`:
# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

DATABASE_URL="mysql://root:root@localhost:3306/crypto"
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=crypto
DB_USERNAME=root
DB_PASSWORD=root

MAIL_MAILER=smtp
MAIL_HOST=sandbox.smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=2255c8d135fd6b
MAIL_PASSWORD=5f55408e25e25b
# MAIL_ENCRYPTION=null
# MAIL_FROM_ADDRESS=""
MAIL_FROM_NAME="${APP_NAME}"

# Google OAuth Configuration
GOOGLE_CLIENT_ID=48322046850-2dhm4q5lbg1rjbem3er0hhljo15tg6th-xxxX.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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-xxxX
GOOGLE_CALLBACK_URL=http://localhost:8080/api/auth/google/callback-xxxX
SESSION_SECRET=your-super-secret-session-key-change-this-in-production-xxxX
