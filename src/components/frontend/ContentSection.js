import React from "react";

const ContentSection = () => {
  return (
    <>
      <div className="py-10 lg:py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="relative overflow-hidden lg:h-full h-[320px] rounded-[30px] ">
              <img
                src="/assets/frontend_assets/bg-1.png"
                alt=""
                className="absolute top-0 left-0 w-full h-full -z-1"
              />
              <div className="flex lg:items-center p-4 lg:py-10 lg:px-8 overflow-hidden h-full">
                <div className="lg:w-2/3 lg:flex-2">
                  <h2 className="inter font-light opacity-90 text-2xl lg:text-[38px] text-white leading-tight">
                    Zero-Cost Account Creation
                  </h2>
                  <p className="inter font-light opacity-70 text-base lg:text-lg text-white max-w-xs mt-2">
                    Start your USD banking journey with Qacent without any
                    charges - no setup fees, You won’t need to worry about any
                    ongoing maintenance costs.
                  </p>
                </div>
                <div className="lg:w-1/3 flex-1">
                  <img
                    src="/assets/frontend_assets/bg-img-1.svg"
                    alt=""
                    className="absolute bottom-0 right-6"
                  />
                </div>
              </div>
            </div>
            <div className="relative overflow-hidden lg:h-full h-[320px] rounded-[30px]">
              <img
                src="/assets/frontend_assets/bg-1.png"
                alt=""
                className="absolute top-0 left-0 w-full h-full -z-1"
              />
              <div className="flex flex-col lg:flex-row items-center p-4 lg:py-10 lg:px-8 overflow-hidden h-full">
                <div className="lg:w-2/3 flex-2 h-full">
                  <h2 className="inter font-light opacity-90 text-2xl lg:text-[38px] text-white leading-tight">
                    Worldwide USD Transactions{" "}
                  </h2>
                  <p className="inter font-light opacity-70 text-base lg:text-lg text-white max-w-sm mt-2">
                    Send and receive US dollars internationally on multiple
                    payment networks including{" "}
                    <span className="font-medium">ACH</span>,{" "}
                    <span className="font-medium">FedNow</span>, and{" "}
                    <span className="font-medium">Wire transfers</span>.
                    Providing a secure method for international financial
                    transactions.
                  </p>
                </div>
                <div className="lg:w-1/3 flex-1 h-full flex items-center justify-center">
                  <div className="pulse__black">
                    <img
                      src="/assets/frontend_assets/world-line.svg"
                      alt=""
                      className=""
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="relative overflow-hidden lg:h-full h-[320px] lg:col-span-2 rounded-[30px]">
              <img
                src="/assets/frontend_assets/content-block-3-bg.png"
                alt=""
                className="absolute top-0 left-0 w-full h-full -z-1"
              />
              <div className="flex flex-col lg:flex-row items-center p-4 lg:py-10 lg:px-8 overflow-hidden">
                <div className="lg:w-1/2 flex-1">
                  <h2 className="inter font-light opacity-90 text-2xl lg:text-[38px] text-white leading-tight">
                    Receive USD Payments From US Companies & Clients{" "}
                  </h2>
                  <p className="inter font-light opacity-70 text-base lg:text-lg text-white max-w-lg mt-2">
                    You can receive money through ACH or Domestic Wire from US
                    banks, PayPal, Wise, Stripe, or Venmo. You can also receive
                    payments from Fiverr, Upwork, Payoneer, Deel, Freelancer,
                    YouTube, TikTok, X, and many more.
                  </p>
                </div>
                <div className="lg:w-1/2 flex-1">
                  <img
                    src="/assets/frontend_assets/content-block-3-img.svg"
                    alt=""
                    className="absolute bottom-0 right-0 rounded-br-[30px]"
                  />
                </div>
              </div>
            </div>
            <div className="relative overflow-hidden lg:h-full h-[320px] rounded-[30px]">
              <img
                src="/assets/frontend_assets/bg-1.png"
                alt=""
                className="absolute top-0 left-0 w-full h-full -z-1"
              />
              <div className="flex flex-col lg:flex-row p-4 lg:py-10 lg:px-8 overflow-hidden h-full">
                <div className="lg:w-2/3 lg:flex-2">
                  <h2 className="inter font-light opacity-90 text-2xl lg:text-[38px] text-white leading-tight">
                    Instant Money Access{" "}
                  </h2>
                  <p className="inter font-light opacity-70 text-base lg:text-lg text-white max-w-[275px] mt-2">
                    Your funds are automatically converted to USDC and made
                    instantly available in your account for immediate use or
                    withdrawal.
                  </p>
                </div>
                <div className="lg:w-1/3 lg:flex-1">
                  <img
                    src="/assets/frontend_assets/block.png"
                    alt=""
                    className="absolute bottom-0 right-2 w-[400px]"
                  />
                </div>
              </div>
            </div>
            <div className="relative overflow-hidden lg:h-full h-[320px] rounded-[30px]">
              <img
                src="/assets/frontend_assets/bg-1.png"
                alt=""
                className="absolute top-0 left-0 w-full h-full -z-1"
              />
              <div className="flex items-center p-4 lg:py-10 lg:px-8 overflow-hidden">
                <div className="">
                  <h2 className="inter font-light opacity-90 text-2xl lg:text-[38px] text-white leading-tight">
                    Digital Currency Exit Options{" "}
                  </h2>
                  <p className="inter font-light opacity-70 text-base lg:text-lg text-white max-w-md mt-2">
                    You Can Effortlessly Convert and transfer your funds to
                    major cryptocurrency platforms such as{" "}
                    <span className="font-medium">Binance</span> or{" "}
                    <span className="font-medium">Bybit</span>, enabling super
                    advantageous <span className="font-medium">P2P</span>{" "}
                    exchange rates through{" "}
                    <span className="font-medium">USDC</span> making it an ideal
                    option for maximizing the value of your transactions.
                  </p>
                </div>
                <div className="">
                  <img
                    src="/assets/frontend_assets/content-block-5-img.svg"
                    alt=""
                    className="absolute bottom-0 right-0"
                  />
                </div>
              </div>
            </div>
            <div className="relative overflow-hidden lg:h-full h-[320px] rounded-[30px]">
              <img
                src="/assets/frontend_assets/bg-1.png"
                alt=""
                className="absolute top-0 left-0 w-full h-full -z-1"
              />
              <div className="flex p-4 lg:py-10 lg:px-8 overflow-hidden pb-25">
                <div className="lg:w-2/3 lg:flex-2 pb-10">
                  <h2 className="inter font-light opacity-90 text-2xl lg:text-[38px] text-white leading-tight">
                    Receive Money From <br /> Families & Friends Abroad{" "}
                  </h2>
                  <p className="inter font-light opacity-70 text-base lg:text-lg text-white max-w-md mt-2">
                    With Qacent, you can also receive money in USD from family
                    members or friends living abroad
                  </p>
                  <img
                    src="/assets/frontend_assets/content-block-6.1-img.svg"
                    alt=""
                    className="absolute bottom-0 left-0"
                  />
                </div>
                {/* <div className="lg:w-1/3 flex-1"> */}
                <img
                  src="/assets/frontend_assets/content-block-6-img.svg"
                  alt=""
                  className="absolute bottom-0 right-0"
                />
                {/* </div> */}
              </div>
            </div>
            <div className="relative overflow-hidden lg:h-full h-[320px] rounded-[30px]">
              <img
                src="/assets/frontend_assets/content-block-7-bg.png"
                alt=""
                className="absolute top-0 left-0 w-full h-full -z-1 object-cover"
              />
              <div className="flex items-center p-4 lg:py-10 lg:px-8 overflow-hidden">
                <div className="lg:w-2/3 lg:flex-2">
                  <h2 className="inter font-light opacity-90 text-2xl lg:text-[38px] text-white leading-tight">
                    Convert USD To USDC Automatically{" "}
                  </h2>
                  <p className="inter font-light opacity-70 text-base lg:text-lg text-white max-w-sm mt-2">
                    When you receive <span className="font-medium">USD</span> on
                    Qacent, your funds are automatically convert into{" "}
                    <span className="font-medium">USDC</span> stablecoin. You
                    can then withdraw to exchanges like{" "}
                    <span className="font-medium">Binance</span> or{" "}
                    <span className="font-medium">Bybit</span> to do{" "}
                    <span className="font-medium">P2P</span> for great rates or
                    any web3 wallet.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContentSection;
