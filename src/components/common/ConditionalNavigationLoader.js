"use client";

import React from "react";
import { usePathname } from "next/navigation";
import { useNavigation } from "@/context/NavigationContext";
import ShimmerLoader from "./ShimmerLoader";

const ConditionalNavigationLoader = () => {
  const pathname = usePathname();
  const { isNavigating, isApiLoading } = useNavigation();

  // Don't show the loader on the home page (/) path
  if (pathname === "/") {
    return null;
  }

  if (pathname.startsWith("/blog/")) {
    return null;
  }



  // Show ShimmerLoader for both navigation and API calls on all other pages
  // The condition is simplified to show the loader for any navigation or API loading
  // This ensures the loader stays visible until API data is fully loaded
  return <ShimmerLoader isLoading={isNavigating || isApiLoading} />;
};

export default ConditionalNavigationLoader;
