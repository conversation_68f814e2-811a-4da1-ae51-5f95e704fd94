const Joi = require("joi");

/**
 * Validation schema for creating a testimonial
 */
const createTestimonialSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(255)
    .required()
    .messages({
      "string.empty": "Name is required",
      "string.min": "Name must be at least 2 characters long",
      "string.max": "Name must not exceed 255 characters",
      "any.required": "Name is required",
    }),

  designation: Joi.string()
    .min(2)
    .max(255)
    .required()
    .messages({
      "string.empty": "Designation is required",
      "string.min": "Designation must be at least 2 characters long",
      "string.max": "Designation must not exceed 255 characters",
      "any.required": "Designation is required",
    }),

  description: Joi.string()
    .allow("")
    .allow(null)
    .optional()
    .messages({
      "string.base": "Description must be a string",
    }),

  photo: Joi.string()
    .allow("")
    .allow(null)
    .optional()
    .messages({
      "string.base": "Photo must be a string",
    }),

  bg_color: Joi.string()
    .allow("")
    .allow(null)
    .optional()
    .pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
    .messages({
      "string.base": "Background color must be a string",
      "string.pattern.base": "Background color must be a valid hex color (e.g., #ffffff or #fff)",
    }),

  status: Joi.string()
    .valid("ACTIVE", "INACTIVE")
    .default("ACTIVE")
    .messages({
      "any.only": "Status must be either ACTIVE or INACTIVE",
    }),
});

/**
 * Validation schema for updating a testimonial
 */
const updateTestimonialSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(255)
    .optional()
    .messages({
      "string.empty": "Name cannot be empty",
      "string.min": "Name must be at least 2 characters long",
      "string.max": "Name must not exceed 255 characters",
    }),

  designation: Joi.string()
    .min(2)
    .max(255)
    .optional()
    .messages({
      "string.empty": "Designation cannot be empty",
      "string.min": "Designation must be at least 2 characters long",
      "string.max": "Designation must not exceed 255 characters",
    }),

  description: Joi.string()
    .allow("")
    .allow(null)
    .optional()
    .messages({
      "string.base": "Description must be a string",
    }),

  photo: Joi.string()
    .allow("")
    .allow(null)
    .optional()
    .messages({
      "string.base": "Photo must be a string",
    }),

  bg_color: Joi.string()
    .allow("")
    .allow(null)
    .optional()
    .pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
    .messages({
      "string.base": "Background color must be a string",
      "string.pattern.base": "Background color must be a valid hex color (e.g., #ffffff or #fff)",
    }),

  status: Joi.string()
    .valid("ACTIVE", "INACTIVE")
    .optional()
    .messages({
      "any.only": "Status must be either ACTIVE or INACTIVE",
    }),
});

/**
 * Validation schema for updating testimonial status
 */
const testimonialStatusSchema = Joi.object({
  status: Joi.string()
    .valid("ACTIVE", "INACTIVE")
    .required()
    .messages({
      "any.only": "Status must be either ACTIVE or INACTIVE",
      "any.required": "Status is required",
    }),
});

module.exports = {
  createTestimonialSchema,
  updateTestimonialSchema,
  testimonialStatusSchema,
};
