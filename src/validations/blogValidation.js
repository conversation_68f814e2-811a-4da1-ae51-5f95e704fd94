const Joi = require("joi");
const { STATUS } = require("../constants/constant");

const createBlogSchema = Joi.object({
  title: Joi.string().min(2).max(255).required().messages({
    "string.empty": "Blog title is required",
    "string.min": "Blog title must be at least 2 characters",
    "string.max": "Blog title cannot exceed 255 characters",
  }),
  description: Joi.string().allow(null, ""),
  status: Joi.string()
    .valid(...Object.values(STATUS))
    .default("ACTIVE")
    .messages({
      "any.only": `Status must be one of: ${Object.values(STATUS).join(", ")}`,
    }),
  image:Joi.any(),
});

const updateBlogSchema = Joi.object({
  title: Joi.string().min(2).max(255).messages({
    "string.min": "Blog title must be at least 2 characters",
    "string.max": "Blog title cannot exceed 255 characters",
  }),
  description: Joi.string().allow(null, ""),
  image:Joi.any(),
  status: Joi.string()
    .valid(...Object.values(STATUS))
    .messages({
      "any.only": `Status must be one of: ${Object.values(STATUS).join(", ")}`,
    }),
}).min(1); // At least one field must be provided


const blogStatusSchema = Joi.object({
  status: Joi.string()
    .valid(...Object.values(STATUS))
    .required()
    .messages({
      "string.empty": "Status is required",
      "any.only": `Status must be one of: ${Object.values(STATUS).join(", ")}`,
    }),
});

module.exports = {
  createBlogSchema,
  updateBlogSchema,
  blogStatusSchema,
};
