const Joi = require("joi");

const INPUT_TYPES = [
  "text",
  "textarea", 
  "number",
  "email",
  "url",
  "select",
  "checkbox",
  "radio",
  "password",
  "file",
  "date",
  "time",
  "datetime"
];

const STATUS_VALUES = ["ACTIVE", "INACTIVE"];

const createSiteSettingsSchema = Joi.object({
  name: Joi.string().min(2).max(255).required().messages({
    "string.empty": "Setting name is required",
    "string.min": "Setting name must be at least 2 characters",
    "string.max": "Setting name cannot exceed 255 characters",
    "any.required": "Setting name is required",
  }),
  description: Joi.string().max(1000).allow(null, "").messages({
    "string.max": "Description cannot exceed 1000 characters",
  }),
  input_type: Joi.string()
    .valid(...INPUT_TYPES)
    .required()
    .messages({
      "string.empty": "Input type is required",
      "any.required": "Input type is required",
      "any.only": `Input type must be one of: ${INPUT_TYPES.join(", ")}`,
    }),
  value: Joi.string().max(5000).allow(null, "").messages({
    "string.max": "Value cannot exceed 5000 characters",
  }),
  status: Joi.string()
    .valid(...STATUS_VALUES)
    .default("ACTIVE")
    .messages({
      "any.only": `Status must be one of: ${STATUS_VALUES.join(", ")}`,
    }),
});

const updateSiteSettingsSchema = Joi.object({
  name: Joi.string().min(2).max(255).messages({
    "string.min": "Setting name must be at least 2 characters",
    "string.max": "Setting name cannot exceed 255 characters",
  }),
  description: Joi.string().max(1000).allow(null, "").messages({
    "string.max": "Description cannot exceed 1000 characters",
  }),
  input_type: Joi.string()
    .valid(...INPUT_TYPES)
    .messages({
      "any.only": `Input type must be one of: ${INPUT_TYPES.join(", ")}`,
    }),
  value: Joi.string().max(5000).allow(null, "").messages({
    "string.max": "Value cannot exceed 5000 characters",
  }),
  status: Joi.string()
    .valid(...STATUS_VALUES)
    .messages({
      "any.only": `Status must be one of: ${STATUS_VALUES.join(", ")}`,
    }),
}).min(1); // At least one field must be provided

const siteSettingsStatusSchema = Joi.object({
  status: Joi.string()
    .valid(...STATUS_VALUES)
    .required()
    .messages({
      "string.empty": "Status is required",
      "any.required": "Status is required",
      "any.only": `Status must be one of: ${STATUS_VALUES.join(", ")}`,
    }),
});

module.exports = {
  createSiteSettingsSchema,
  updateSiteSettingsSchema,
  siteSettingsStatusSchema,
  INPUT_TYPES,
  STATUS_VALUES,
};
