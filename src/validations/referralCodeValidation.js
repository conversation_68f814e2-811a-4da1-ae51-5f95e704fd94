const Joi = require("joi");
const { STATUS } = require("../constants/constant");

const createReferralCodeSchema = Joi.object({
  code: Joi.string().min(3).max(50).required().messages({
    "string.empty": "Referral code is required",
    "string.min": "Referral code must be at least 3 characters",
    "string.max": "Referral code cannot exceed 50 characters",
    "any.required": "Referral code is required",
  }),
  user_id: Joi.alternatives().try(
    Joi.number(),
    Joi.string().pattern(/^\d+$/)
  ).allow(null).messages({
    "number.base": "User ID must be a number",
  }),
  status: Joi.string()
    .valid("ACTIVE", "INACTIVE", "USED")
    .default("ACTIVE")
    .messages({
      "any.only": "Status must be one of: ACTIVE, INACTIVE, USED",
    }),
});

const updateReferralCodeSchema = Joi.object({
  code: Joi.string().min(3).max(50).messages({
    "string.min": "Referral code must be at least 3 characters",
    "string.max": "Referral code cannot exceed 50 characters",
  }),
  user_id: Joi.alternatives().try(
    <PERSON>i.number(),
    Joi.string().pattern(/^\d+$/)
  ).allow(null).messages({
    "number.base": "User ID must be a number",
  }),
  status: Joi.string()
    .valid("ACTIVE", "INACTIVE", "USED")
    .messages({
      "any.only": "Status must be one of: ACTIVE, INACTIVE, USED",
    }),
}).min(1); // At least one field must be provided

const referralCodeStatusSchema = Joi.object({
  status: Joi.string()
    .valid("ACTIVE", "INACTIVE", "USED")
    .required()
    .messages({
      "string.empty": "Status is required",
      "any.required": "Status is required",
      "any.only": "Status must be one of: ACTIVE, INACTIVE, USED",
    }),
});

const validateReferralCodeSchema = Joi.object({
  code: Joi.string().min(3).max(50).required().messages({
    "string.empty": "Referral code is required",
    "string.min": "Referral code must be at least 3 characters",
    "string.max": "Referral code cannot exceed 50 characters",
    "any.required": "Referral code is required",
  }),
});

const checkReferralCodeSchema = Joi.object({
  value: Joi.string().min(3).max(50).required().messages({
    "string.empty": "Referral code value is required",
    "string.min": "Referral code must be at least 3 characters",
    "string.max": "Referral code cannot exceed 50 characters",
    "any.required": "Referral code value is required",
  }),
});

module.exports = {
  createReferralCodeSchema,
  updateReferralCodeSchema,
  referralCodeStatusSchema,
  validateReferralCodeSchema,
  checkReferralCodeSchema,
};
