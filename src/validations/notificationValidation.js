const Joi = require("joi");

/**
 * Validation schema for creating a notification
 */
const createNotificationSchema = Joi.object({
  text: Joi.string()
    .min(1)
    .max(1000)
    .required()
    .messages({
      "string.empty": "Notification text is required",
      "string.min": "Notification text must be at least 1 character long",
      "string.max": "Notification text must not exceed 1000 characters",
      "any.required": "Notification text is required",
    }),

  user_id: Joi.alternatives()
    .try(
      Joi.number().integer().positive(),
      Joi.string().pattern(/^\d+$/),
      Joi.allow(null)
    )
    .optional()
    .messages({
      "number.base": "User ID must be a number",
      "number.integer": "User ID must be an integer",
      "number.positive": "User ID must be positive",
      "string.pattern.base": "User ID must be a valid number",
    }),

  is_read: Joi.boolean()
    .default(false)
    .optional()
    .messages({
      "boolean.base": "is_read must be a boolean value",
    }),
});

/**
 * Validation schema for updating a notification
 */
const updateNotificationSchema = Joi.object({
  text: Joi.string()
    .min(1)
    .max(1000)
    .optional()
    .messages({
      "string.empty": "Notification text cannot be empty",
      "string.min": "Notification text must be at least 1 character long",
      "string.max": "Notification text must not exceed 1000 characters",
    }),

  user_id: Joi.alternatives()
    .try(
      Joi.number().integer().positive(),
      Joi.string().pattern(/^\d+$/),
      Joi.allow(null)
    )
    .optional()
    .messages({
      "number.base": "User ID must be a number",
      "number.integer": "User ID must be an integer",
      "number.positive": "User ID must be positive",
      "string.pattern.base": "User ID must be a valid number",
    }),

  is_read: Joi.boolean()
    .optional()
    .messages({
      "boolean.base": "is_read must be a boolean value",
    }),
});

/**
 * Validation schema for marking notification as read
 */
const markAsReadSchema = Joi.object({
  is_read: Joi.boolean()
    .required()
    .messages({
      "boolean.base": "is_read must be a boolean value",
      "any.required": "is_read is required",
    }),
});

/**
 * Validation schema for bulk operations
 */
const bulkMarkAsReadSchema = Joi.object({
  notification_ids: Joi.array()
    .items(
      Joi.alternatives().try(
        Joi.number().integer().positive(),
        Joi.string().pattern(/^\d+$/)
      )
    )
    .min(1)
    .required()
    .messages({
      "array.base": "notification_ids must be an array",
      "array.min": "At least one notification ID is required",
      "any.required": "notification_ids is required",
    }),

  is_read: Joi.boolean()
    .default(true)
    .optional()
    .messages({
      "boolean.base": "is_read must be a boolean value",
    }),
});

module.exports = {
  createNotificationSchema,
  updateNotificationSchema,
  markAsReadSchema,
  bulkMarkAsReadSchema,
};
