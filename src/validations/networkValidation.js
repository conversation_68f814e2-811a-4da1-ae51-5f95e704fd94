const Joi = require("joi");
const { STATUS } = require("../constants/constant");

const createNetworkSchema = Joi.object({
  name: Joi.string().min(2).max(255).required().messages({
    "string.empty": "Network name is required",
    "string.min": "Network name must be at least 2 characters",
    "string.max": "Network name cannot exceed 255 characters",
  }),
  // code is auto-generated, so it's not included in the validation
  image: Joi.string().allow(null, "").messages({
    "string.base": "Image must be a string",
  }),
  currency_id: Joi.alternatives()
    .try(Joi.number(), Joi.string().pattern(/^\d+$/))
    .required()
    .messages({
      "any.required": "Currency ID is required",
    }),
  order: Joi.alternatives()
    .try(Joi.number().integer(), Joi.string().pattern(/^\d+$/))
    .allow(null),
  enable_extra_field: Joi.alternatives()
    .try(Joi.boolean(), Joi.string().valid("true", "false"))
    .default(false),
  status: Joi.string()
    .valid(...Object.values(STATUS))
    .default("ACTIVE")
    .messages({
      "any.only": `Status must be one of: ${Object.values(STATUS).join(", ")}`,
    }),
});

const updateNetworkSchema = Joi.object({
  name: Joi.string().min(2).max(255).messages({
    "string.min": "Network name must be at least 2 characters",
    "string.max": "Network name cannot exceed 255 characters",
  }),
  // code is auto-generated and cannot be updated
  image: Joi.string().allow(null, "").messages({
    "string.base": "Image must be a string",
  }),
  currency_id: Joi.alternatives().try(
    Joi.number(),
    Joi.string().pattern(/^\d+$/)
  ),
  order: Joi.alternatives()
    .try(Joi.number().integer(), Joi.string().pattern(/^\d+$/))
    .allow(null),
  enable_extra_field: Joi.alternatives().try(
    Joi.boolean(),
    Joi.string().valid("true", "false")
  ),
  status: Joi.string()
    .valid(...Object.values(STATUS))
    .messages({
      "any.only": `Status must be one of: ${Object.values(STATUS).join(", ")}`,
    }),
}).min(1); // At least one field must be provided

const networkStatusSchema = Joi.object({
  status: Joi.string()
    .valid(...Object.values(STATUS))
    .required()
    .messages({
      "string.empty": "Status is required",
      "any.only": `Status must be one of: ${Object.values(STATUS).join(", ")}`,
    }),
});

module.exports = {
  createNetworkSchema,
  updateNetworkSchema,
  networkStatusSchema,
};
