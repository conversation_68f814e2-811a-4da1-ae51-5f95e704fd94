var express = require("express");

// Try to load passport, fallback if not available
let passport;
try {
  passport = require("passport");
} catch (error) {
  console.log("Passport not available. Google OAuth routes will be disabled.");
}

const {
  register,
  login,
  adminLogin,
  googleCallback,
} = require("../controllers/authController");
var Route = express.Router();

Route.post("/register", register);
Route.post("/login", login);
Route.post("/admin-login", adminLogin);

// Google OAuth routes (only if passport is available)
if (passport) {
  Route.get(
    "/google",
    passport.authenticate("google", { scope: ["profile", "email"] })
  );
  Route.get(
    "/google/callback",
    passport.authenticate("google", { session: false }),
    googleCallback
  );
} else {
  // Fallback routes when Google OAuth is not available
  Route.get("/google", (req, res) => {
    res.status(503).json({
      error: "Google OAuth not available",
      message:
        "Please install required dependencies: express-session, passport, passport-google-oauth20",
    });
  });

  Route.get("/google/callback", (req, res) => {
    res.status(503).json({
      error: "Google OAuth not available",
      message:
        "Please install required dependencies: express-session, passport, passport-google-oauth20",
    });
  });
}

module.exports = Route;
