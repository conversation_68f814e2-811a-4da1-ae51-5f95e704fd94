const express = require("express");
const Route = express.Router();
const countryController = require("../controllers/countryController");
const clientBlogController = require("../controllers/clientBlogController");
const userController = require("../controllers/userController");
const testimonialController = require("../controllers/testimonialController");
const referralCodeController = require("../controllers/referralCodeController");
const siteSettingsController = require("../controllers/siteSettingsController");

Route.get("/dropdown/countries",countryController.getActiveCountriesDropdown);
/* Blog routes */
Route.get("/blogs/recent", clientBlogController.getRecentBlogs);
Route.get("/blogs/:id", clientBlogController.getBlogDetailsWithRelated);

Route.post("/check-email", userController.checkEmail);

/* Testimonial routes */
Route.get("/testimonials/active", testimonialController.getActiveTestimonials);

/* Referral Code routes */
Route.post("/referral-codes/validate", referralCodeController.validateReferralCode);
Route.post("/referral-codes/check", referralCodeController.checkReferralCode);

/* Site Settings routes */
Route.post("/site-settings/check", siteSettingsController.checkSiteSetting);

module.exports = Route;
