const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const { successResponse, errorResponse } = require("../utils/responseHelper");

/**
 * Get recent blog posts (limited to 3)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with recent blog posts
 */
async function getRecentBlogs(req, res) {
  try {
    const recentBlogs = await prisma.blog.findMany({
      where: {
        status: "ACTIVE",
      },
      take: 3,
      orderBy: {
        created_at: "desc",
      },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
          },
        },
      },
    });

    // Format the response
    const formattedBlogs = recentBlogs.map((blog) => ({
      id: blog.id.toString(),
      title: blog.title,
      image: blog.image,
      description: blog.description ? 
        blog.description.length > 150 ? 
          `${blog.description.substring(0, 150)}...` : 
          blog.description : 
        null,
      created_at: blog.created_at,
      author: blog.user.full_name,
    }));

    return successResponse(
      res,
      formattedBlogs,
      "Recent blogs retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve recent blogs",
      500
    );
  }
}

/**
 * Get blog details by ID with related recent posts
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with blog details and related recent posts
 */
async function getBlogDetailsWithRelated(req, res) {
  try {
    const blogId = req.params.id;

    // Get the blog details
    const blog = await prisma.blog.findUnique({
      where: {
        id: BigInt(blogId),
        status: "ACTIVE",
      },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
          },
        },
      },
    });

    if (!blog) {
      return errorResponse(
        res,
        `Blog with ID ${blogId} not found`,
        "Blog not found",
        404
      );
    }

    // Get 4 recent related blogs (excluding the current blog)
    const relatedBlogs = await prisma.blog.findMany({
      where: {
        status: "ACTIVE",
        id: {
          not: BigInt(blogId),
        },
      },
      take: 4,
      orderBy: {
        created_at: "desc",
      },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
          },
        },
      },
    });

    // Format the blog details
    const formattedBlog = {
      id: blog.id.toString(),
      title: blog.title,
      image: blog.image,
      description: blog.description,
      created_at: blog.created_at,
      author: blog.user.full_name,
    };

    // Format the related blogs
    const formattedRelatedBlogs = relatedBlogs.map((relatedBlog) => ({
      id: relatedBlog.id.toString(),
      title: relatedBlog.title,
      image: relatedBlog.image,
      description: relatedBlog.description ? 
        relatedBlog.description.length > 150 ? 
          `${relatedBlog.description.substring(0, 150)}...` : 
          relatedBlog.description : 
        null,
      created_at: relatedBlog.created_at,
      author: relatedBlog.user.full_name,
    }));

    // Combine blog details and related blogs in the response
    const response = {
      blog: formattedBlog,
      related_blogs: formattedRelatedBlogs,
    };

    return successResponse(
      res,
      response,
      "Blog details retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve blog details",
      500
    );
  }
}

module.exports = {
  getRecentBlogs,
  getBlogDetailsWithRelated,
};
