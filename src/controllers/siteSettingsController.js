const siteSettingsService = require("../services/siteSettingsService");
const { successResponse, errorResponse } = require("../utils/responseHelper");
const {
  createSiteSettingsSchema,
  updateSiteSettingsSchema,
  siteSettingsStatusSchema,
} = require("../validations/siteSettingsValidation");

/**
 * Get all site settings
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with site settings
 */
async function getSiteSettings(req, res) {
  try {
    const siteSettings = await siteSettingsService.getAllSiteSettings(req);
    return successResponse(
      res,
      siteSettings,
      "Site settings retrieved successfully",
      200,
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve site settings",
      500,
    );
  }
}

/**
 * Get a site setting by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with site setting
 */
async function getSiteSettingById(req, res) {
  try {
    const siteSetting = await siteSettingsService.getSiteSettingById(req.params.id);
    return successResponse(
      res,
      siteSetting,
      "Site setting retrieved successfully",
      200,
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve site setting",
      404
    );
  }
}

/**
 * Get a site setting by name
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with site setting
 */
async function getSiteSettingByName(req, res) {
  try {
    const siteSetting = await siteSettingsService.getSiteSettingByName(req.params.name);
    return successResponse(
      res,
      siteSetting,
      "Site setting retrieved successfully",
      200,
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve site setting",
      404
    );
  }
}

/**
 * Create a new site setting
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with created site setting
 */
async function createSiteSetting(req, res) {
  try {
    // Validate request body against schema
    const { error } = createSiteSettingsSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        422
      );
    }

    const siteSetting = await siteSettingsService.createSiteSetting(req.body);
    return successResponse(
      res,
      siteSetting,
      "Site setting created successfully",
      201
    );
  } catch (error) {
    return errorResponse(res, error.message, "Site setting creation failed", 400);
  }
}

/**
 * Update a site setting
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated site setting
 */
async function updateSiteSetting(req, res) {
  try {
    // Validate request body against schema
    const { error } = updateSiteSettingsSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        422
      );
    }

    const siteSetting = await siteSettingsService.updateSiteSetting(
      req.params.id,
      req.body
    );
    return successResponse(
      res,
      siteSetting,
      "Site setting updated successfully",
      200
    );
  } catch (error) {
    return errorResponse(res, error.message, "Site setting update failed", 400);
  }
}

/**
 * Update a site setting's status
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated site setting
 */
async function updateSiteSettingStatus(req, res) {
  try {
    // Validate request body against schema
    const { error } = siteSettingsStatusSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        422
      );
    }

    const siteSetting = await siteSettingsService.updateSiteSettingStatus(
      req.params.id,
      req.body.status
    );
    return successResponse(
      res,
      siteSetting,
      "Status updated successfully",
      200
    );
  } catch (error) {
    return errorResponse(res, error.message, "Status update failed", 400);
  }
}

/**
 * Delete a site setting
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with deleted site setting
 */
async function deleteSiteSetting(req, res) {
  try {
    const siteSetting = await siteSettingsService.deleteSiteSetting(req.params.id);
    return successResponse(
      res,
      siteSetting,
      "Site setting deleted successfully",
      200
    );
  } catch (error) {
    return errorResponse(res, error.message, "Site setting deletion failed", 400);
  }
}

/**
 * Check a site setting status (similar to check-email)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with boolean result
 */
async function checkSiteSetting(req, res) {
  try {
    const value = req.body.value;

    // Check if value was provided
    if (!value) {
      return errorResponse(res, null, "Site setting name is required", 400);
    }

    const isActive = await siteSettingsService.checkSiteSetting(value);

    if (isActive) {
      return successResponse(res, true, "Site setting is active", 200);
    } else {
      return successResponse(res, false, "Site setting is not active", 200);
    }
  } catch (error) {
    return errorResponse(res, error.message, "Site setting check failed", 500);
  }
}

module.exports = {
  getSiteSettings,
  getSiteSettingById,
  getSiteSettingByName,
  createSiteSetting,
  updateSiteSetting,
  updateSiteSettingStatus,
  deleteSiteSetting,
  checkSiteSetting,
};
