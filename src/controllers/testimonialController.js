const testimonialService = require("../services/testimonialService");
const { successResponse, errorResponse } = require("../utils/responseHelper");
const {
  createTestimonialSchema,
  updateTestimonialSchema,
  testimonialStatusSchema,
} = require("../validations/testimonialValidation");
const upload = require("../utils/fileUpload");

// Single file upload middleware for testimonial photo
const uploadPhoto = upload.single("photo");

/**
 * Get all testimonials with pagination, search, and filtering
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with testimonials and pagination metadata
 */
async function getTestimonials(req, res) {
  try {
    const testimonials = await testimonialService.getAllTestimonials(req);
    return successResponse(
      res,
      testimonials,
      "Testimonials retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve testimonials",
      500
    );
  }
}

/**
 * Get testimonial by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with testimonial
 */
async function getTestimonialById(req, res) {
  try {
    const testimonial = await testimonialService.getTestimonialById(req.params.id);
    return successResponse(
      res,
      testimonial,
      "Testimonial retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve testimonial",
      error.message.includes("not found") ? 404 : 500
    );
  }
}

/**
 * Create testimonial
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with created testimonial
 */
async function createTestimonial(req, res) {
  try {
    uploadPhoto(req, res, async (err) => {
      if (err) {
        return errorResponse(res, err.message, "File upload failed", 400);
      }

      try {
        // Add file path to request body if file was uploaded
        if (req.file) {
          req.body.photo = `/uploads/${req.file.filename}`;
        }

        // Validate request body against createTestimonialSchema
        const { error } = createTestimonialSchema.validate(req.body);
        if (error) {
          return errorResponse(
            res,
            error.details[0].message,
            "Validation failed",
            400
          );
        }

        const testimonial = await testimonialService.createTestimonial(req.body);
        return successResponse(
          res,
          testimonial,
          "Testimonial created successfully",
          201
        );
      } catch (innerError) {
        return errorResponse(
          res,
          innerError.message,
          "Testimonial creation failed",
          400
        );
      }
    });
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Testimonial creation failed",
      400
    );
  }
}

/**
 * Update testimonial
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated testimonial
 */
async function updateTestimonial(req, res) {
  try {
    uploadPhoto(req, res, async (err) => {
      if (err) {
        return errorResponse(res, err.message, "File upload failed", 400);
      }

      try {
        // Add file path to request body if file was uploaded
        if (req.file) {
          req.body.photo = `/uploads/${req.file.filename}`;
        }

        // Validate request body against updateTestimonialSchema
        const { error } = updateTestimonialSchema.validate(req.body);
        if (error) {
          return errorResponse(
            res,
            error.details[0].message,
            "Validation failed",
            400
          );
        }

        const testimonial = await testimonialService.updateTestimonial(
          req.params.id,
          req.body
        );
        return successResponse(
          res,
          testimonial,
          "Testimonial updated successfully",
          200
        );
      } catch (innerError) {
        return errorResponse(
          res,
          innerError.message,
          "Testimonial update failed",
          innerError.message.includes("not found") ? 404 : 400
        );
      }
    });
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Testimonial update failed",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

/**
 * Update testimonial status
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated testimonial
 */
async function updateTestimonialStatus(req, res) {
  try {
    // Validate request body against testimonialStatusSchema
    const { error } = testimonialStatusSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        400
      );
    }

    const testimonial = await testimonialService.updateTestimonialStatus(
      req.params.id,
      req.body.status
    );
    return successResponse(
      res,
      testimonial,
      "Testimonial status updated successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Testimonial status update failed",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

/**
 * Delete testimonial
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with deleted testimonial
 */
async function deleteTestimonial(req, res) {
  try {
    const testimonial = await testimonialService.deleteTestimonial(req.params.id);
    return successResponse(
      res,
      testimonial,
      "Testimonial deleted successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Testimonial deletion failed",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

/**
 * Get active testimonials for public display
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with active testimonials
 */
async function getActiveTestimonials(req, res) {
  try {
    const testimonials = await testimonialService.getActiveTestimonials();
    return successResponse(
      res,
      testimonials,
      "Active testimonials retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve active testimonials",
      500
    );
  }
}

module.exports = {
  getTestimonials,
  getTestimonialById,
  createTestimonial,
  updateTestimonial,
  updateTestimonialStatus,
  deleteTestimonial,
  getActiveTestimonials,
};
