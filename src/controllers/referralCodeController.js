const referralCodeService = require("../services/referralCodeService");
const { successResponse, errorResponse } = require("../utils/responseHelper");
const {
  createReferralCodeSchema,
  updateReferralCodeSchema,
  referralCodeStatusSchema,
  validateReferralCodeSchema,
} = require("../validations/referralCodeValidation");

/**
 * Get all referral codes
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with referral codes
 */
async function getReferralCodes(req, res) {
  try {
    const referralCodes = await referralCodeService.getAllReferralCodes(req);
    return successResponse(
      res,
      referralCodes,
      "Invitation codes retrieved successfully",
      200,
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve referral codes",
      500,
    );
  }
}

/**
 * Get a referral code by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with referral code
 */
async function getReferralCodeById(req, res) {
  try {
    const referralCode = await referralCodeService.getReferralCodeById(req.params.id);
    return successResponse(
      res,
      referralCode,
      "Invitation code retrieved successfully",
      200,
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve referral code",
      404
    );
  }
}

/**
 * Create a new referral code
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with created referral code
 */
async function createReferralCode(req, res) {
  try {
    // Validate request body against schema
    const { error } = createReferralCodeSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        422
      );
    }

    const referralCode = await referralCodeService.createReferralCode(req.body);
    return successResponse(
      res,
      referralCode,
      "Invitation code created successfully",
      201
    );
  } catch (error) {
    return errorResponse(res, error.message, "Invitation code creation failed", 400);
  }
}

/**
 * Update a referral code
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated referral code
 */
async function updateReferralCode(req, res) {
  try {
    // Validate request body against schema
    const { error } = updateReferralCodeSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        422
      );
    }

    const referralCode = await referralCodeService.updateReferralCode(
      req.params.id,
      req.body
    );
    return successResponse(
      res,
      referralCode,
      "Invitation code updated successfully",
      200
    );
  } catch (error) {
    return errorResponse(res, error.message, "Invitation code update failed", 400);
  }
}

/**
 * Update a referral code's status
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated referral code
 */
async function updateReferralCodeStatus(req, res) {
  try {
    // Validate request body against schema
    const { error } = referralCodeStatusSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        422
      );
    }

    const referralCode = await referralCodeService.updateReferralCodeStatus(
      req.params.id,
      req.body.status
    );
    return successResponse(
      res,
      referralCode,
      "Status updated successfully",
      200
    );
  } catch (error) {
    return errorResponse(res, error.message, "Status update failed", 400);
  }
}

/**
 * Delete a referral code
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with deleted referral code
 */
async function deleteReferralCode(req, res) {
  try {
    const referralCode = await referralCodeService.deleteReferralCode(req.params.id);
    return successResponse(
      res,
      referralCode,
      "Invitation code deleted successfully",
      200
    );
  } catch (error) {
    return errorResponse(res, error.message, "Invitation code deletion failed", 400);
  }
}

/**
 * Validate a referral code
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with validation result
 */
async function validateReferralCode(req, res) {
  try {
    // Validate request body against schema
    const { error } = validateReferralCodeSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        422
      );
    }

    const validationResult = await referralCodeService.validateReferralCode(req.body.code);
    return successResponse(
      res,
      validationResult,
      "Invitation code validation completed",
      200
    );
  } catch (error) {
    return errorResponse(res, error.message, "Invitation code validation failed", 400);
  }
}

/**
 * Check a referral code status (similar to check-email)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with boolean result
 */
async function checkReferralCode(req, res) {
  try {
    const value = req.body.value;

    // Check if value was provided
    if (!value) {
      return errorResponse(res, null, "Invitation code value is required", 400);
    }

    const isActive = await referralCodeService.checkReferralCode(value);

    if (isActive) {
      return successResponse(res, true, "Invitation code is active", 200);
    } else {
      return successResponse(res, false, "Invitation code is not active", 200);
    }
  } catch (error) {
    return errorResponse(res, error.message, "Invitation code check failed", 500);
  }
}

module.exports = {
  getReferralCodes,
  getReferralCodeById,
  createReferralCode,
  updateReferralCode,
  updateReferralCodeStatus,
  deleteReferralCode,
  validateReferralCode,
  checkReferralCode,
};
