const notificationService = require("../services/notificationService");
const { successResponse, errorResponse } = require("../utils/responseHelper");
const {
  createNotificationSchema,
  updateNotificationSchema,
  markAsReadSchema,
  bulkMarkAsReadSchema,
} = require("../validations/notificationValidation");
const Auth = require("../utils/auth");

/**
 * Get all notifications with pagination, search, and filtering (Admin)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with notifications and pagination metadata
 */
async function getNotifications(req, res) {
  try {
    const notifications = await notificationService.getAllNotifications(req);
    return successResponse(
      res,
      notifications,
      "Notifications retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve notifications",
      500
    );
  }
}

/**
 * Get notifications for authenticated user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with user notifications
 */
async function getUserNotifications(req, res) {
  try {
    const userId = await Auth.id(req);
    if (!userId) {
      return errorResponse(res, "User not authenticated", "Authentication failed", 401);
    }

    const notifications = await notificationService.getUserNotifications(userId, req.query);
    return successResponse(
      res,
      notifications,
      "User notifications retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve user notifications",
      500
    );
  }
}

/**
 * Get notification by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with notification
 */
async function getNotificationById(req, res) {
  try {
    const notification = await notificationService.getNotificationById(req.params.id);
    return successResponse(
      res,
      notification,
      "Notification retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve notification",
      error.message.includes("not found") ? 404 : 500
    );
  }
}

/**
 * Create notification
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with created notification
 */
async function createNotification(req, res) {
  try {
    // Validate request body against createNotificationSchema
    const { error } = createNotificationSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        400
      );
    }

    const notification = await notificationService.createNotification(req.body);
    return successResponse(
      res,
      notification,
      "Notification created successfully",
      201
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Notification creation failed",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

/**
 * Update notification
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated notification
 */
async function updateNotification(req, res) {
  try {
    // Validate request body against updateNotificationSchema
    const { error } = updateNotificationSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        400
      );
    }

    const notification = await notificationService.updateNotification(
      req.params.id,
      req.body
    );
    return successResponse(
      res,
      notification,
      "Notification updated successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Notification update failed",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

/**
 * Mark notification as read/unread
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated notification
 */
async function markAsRead(req, res) {
  try {
    // Validate request body against markAsReadSchema
    const { error } = markAsReadSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        400
      );
    }

    const notification = await notificationService.markAsRead(
      req.params.id,
      req.body.is_read
    );
    return successResponse(
      res,
      notification,
      "Notification status updated successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to update notification status",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

/**
 * Bulk mark notifications as read/unread
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with update result
 */
async function bulkMarkAsRead(req, res) {
  try {
    // Validate request body against bulkMarkAsReadSchema
    const { error } = bulkMarkAsReadSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        400
      );
    }

    const result = await notificationService.bulkMarkAsRead(
      req.body.notification_ids,
      req.body.is_read
    );
    return successResponse(
      res,
      result,
      "Notifications updated successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to update notifications",
      400
    );
  }
}

/**
 * Delete notification
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with deleted notification
 */
async function deleteNotification(req, res) {
  try {
    const notification = await notificationService.deleteNotification(req.params.id);
    return successResponse(
      res,
      notification,
      "Notification deleted successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Notification deletion failed",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

/**
 * Get unread notification count for authenticated user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with unread count
 */
async function getUnreadCount(req, res) {
  try {
    const userId = await Auth.id(req);
    if (!userId) {
      return errorResponse(res, "User not authenticated", "Authentication failed", 401);
    }

    const result = await notificationService.getUnreadCount(userId);
    return successResponse(
      res,
      result,
      "Unread count retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve unread count",
      500
    );
  }
}

module.exports = {
  getNotifications,
  getUserNotifications,
  getNotificationById,
  createNotification,
  updateNotification,
  markAsRead,
  bulkMarkAsRead,
  deleteNotification,
  getUnreadCount,
};
