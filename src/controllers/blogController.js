const blogService = require("../services/blogService");
const { successResponse, errorResponse } = require("../utils/responseHelper");
const {
  createBlogSchema,
  updateBlogSchema,
  blogStatusSchema,
} = require("../validations/blogValidation");
const upload = require("../utils/fileUpload");

// Single file upload middleware for blog image
const uploadImage = upload.single("image");

/**
 * Get all blogs with pagination, search, and filtering
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with blogs and pagination metadata
 */
async function getBlogs(req, res) {
  try {
    const blogs = await blogService.getAllBlogs(req);
    return successResponse(
      res,
      blogs,
      "Blogs retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve blogs",
      500
    );
  }
}

/**
 * Get blog by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with blog
 */
async function getBlogById(req, res) {
  try {
    const blog = await blogService.getBlogById(req.params.id);
    return successResponse(
      res,
      blog,
      "Blog retrieved successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Failed to retrieve blog",
      error.message.includes("not found") ? 404 : 500
    );
  }
}

/**
 * Create blog
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with created blog
 */
async function createBlog(req, res) {
  try {
    uploadImage(req, res, async (err) => {
      if (err) {
        return errorResponse(res, err.message, "File upload failed", 400);
      }

      try {
        // Add file path to request body if file was uploaded
        if (req.file) {
          req.body.image = `/uploads/${req.file.filename}`;
        }

        // Validate request body against createBlogSchema
        const { error } = createBlogSchema.validate(req.body);
        if (error) {
          return errorResponse(
            res,
            error.details[0].message,
            "Validation failed",
            400
          );
        }

        const blog = await blogService.createBlog(req.body, req);
        return successResponse(
          res,
          blog,
          "Blog created successfully",
          201
        );
      } catch (innerError) {
        return errorResponse(
          res,
          innerError.message,
          "Blog creation failed",
          400
        );
      }
    });
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Blog creation failed",
      400
    );
  }
}

/**
 * Update blog
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated blog
 */
async function updateBlog(req, res) {
  try {
    uploadImage(req, res, async (err) => {
      if (err) {
        return errorResponse(res, err.message, "File upload failed", 400);
      }

      try {
        // Add file path to request body if file was uploaded
        if (req.file) {
          req.body.image = `/uploads/${req.file.filename}`;
        }

        // Validate request body against updateBlogSchema
        const { error } = updateBlogSchema.validate(req.body);
        if (error) {
          return errorResponse(
            res,
            error.details[0].message,
            "Validation failed",
            400
          );
        }

        const blog = await blogService.updateBlog(req.params.id, req.body);
        return successResponse(
          res,
          blog,
          "Blog updated successfully",
          200
        );
      } catch (innerError) {
        return errorResponse(
          res,
          innerError.message,
          "Blog update failed",
          innerError.message.includes("not found") ? 404 : 400
        );
      }
    });
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Blog update failed",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

/**
 * Update blog status
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with updated blog
 */
async function updateBlogStatus(req, res) {
  try {
    // Validate request body against blogStatusSchema
    const { error } = blogStatusSchema.validate(req.body);
    if (error) {
      return errorResponse(
        res,
        error.details[0].message,
        "Validation failed",
        400
      );
    }

    const blog = await blogService.updateBlogStatus(
      req.params.id,
      req.body.status
    );
    return successResponse(
      res,
      blog,
      "Blog status updated successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Blog status update failed",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

/**
 * Delete blog
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with deleted blog
 */
async function deleteBlog(req, res) {
  try {
    const blog = await blogService.deleteBlog(req.params.id);
    return successResponse(
      res,
      blog,
      "Blog deleted successfully",
      200
    );
  } catch (error) {
    return errorResponse(
      res,
      error.message,
      "Blog deletion failed",
      error.message.includes("not found") ? 404 : 400
    );
  }
}

module.exports = {
  getBlogs,
  getBlogById,
  createBlog,
  updateBlog,
  updateBlogStatus,
  deleteBlog,
};
