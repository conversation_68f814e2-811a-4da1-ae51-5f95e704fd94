const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const {
  buildSearchCondition,
  buildPagination,
  generatePaginationMetadata,
  normalizeQuery,
} = require("../utils/helpers");
const {
  createSiteSettingsSchema,
  updateSiteSettingsSchema,
} = require("../validations/siteSettingsValidation");

/**
 * Get all site settings with pagination, search, and filter
 * @param {Object} req - Request object
 * @returns {Object} Site settings with pagination metadata
 */
async function getAllSiteSettings(req) {
  try {
    const {
      page = 1,
      perPage = 10,
      search,
      status,
      input_type,
      sort,
      order,
    } = normalizeQuery(req.query);
    const pageInt = parseInt(page, 10);
    const perPageInt = parseInt(perPage, 10);

    // Build search condition
    const searchCondition = buildSearchCondition(search, [
      "name",
      "description",
    ]);

    // Build status filter condition
    const statusCondition = status ? { status } : {};

    // Build input type filter
    const inputTypeFilter = input_type ? { input_type } : {};

    // Combine conditions
    const whereCondition = {
      AND: [searchCondition, statusCondition, inputTypeFilter],
    };

    // Count total records
    const totalCount = await prisma.siteSettings.count({
      where: whereCondition,
    });

    // Build pagination
    const { skip, take } = buildPagination(pageInt, perPageInt);

    // Build sort order
    const orderBy = {};
    if (sort && order) {
      orderBy[sort] = order.toLowerCase();
    } else {
      // Default sorting
      orderBy.id = "desc";
    }

    // Get site settings with pagination
    const siteSettings = await prisma.siteSettings.findMany({
      where: whereCondition,
      skip,
      take,
      orderBy,
    });

    // Format site settings for response
    const formattedSiteSettings = siteSettings.map((setting) => ({
      ...setting,
      id: setting.id.toString(),
    }));

    // Generate pagination metadata
    const pagination = generatePaginationMetadata(
      req,
      pageInt,
      totalCount,
      perPageInt
    );

    return {
      ...pagination,
      data: formattedSiteSettings,
    };
  } catch (error) {
    throw new Error(`Failed to retrieve site settings: ${error.message}`);
  }
}

/**
 * Get a site setting by ID
 * @param {string} id - Site setting ID
 * @returns {Object} Site setting
 */
async function getSiteSettingById(id) {
  try {
    const siteSetting = await prisma.siteSettings.findUnique({
      where: { id: BigInt(id) },
    });

    if (!siteSetting) {
      throw new Error("Site setting not found");
    }

    // Format site setting for response
    return {
      ...siteSetting,
      id: siteSetting.id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to retrieve site setting: ${error.message}`);
  }
}

/**
 * Get a site setting by name
 * @param {string} name - Site setting name
 * @returns {Object} Site setting
 */
async function getSiteSettingByName(name) {
  try {
    const siteSetting = await prisma.siteSettings.findUnique({
      where: { name },
    });

    if (!siteSetting) {
      throw new Error("Site setting not found");
    }

    // Format site setting for response
    return {
      ...siteSetting,
      id: siteSetting.id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to retrieve site setting: ${error.message}`);
  }
}

/**
 * Create a new site setting
 * @param {Object} data - Site setting data
 * @returns {Object} Created site setting
 */
async function createSiteSetting(data) {
  try {
    // Validate site setting data
    const { error } = createSiteSettingsSchema.validate(data);

    if (error) {
      throw new Error(error.details[0].message);
    }

    // Check if setting name already exists
    const existingSetting = await prisma.siteSettings.findFirst({
      where: { name: data.name },
    });

    if (existingSetting) {
      throw new Error("Setting name already exists");
    }

    // Create the site setting
    const siteSetting = await prisma.siteSettings.create({
      data: {
        name: data.name,
        description: data.description || null,
        input_type: data.input_type,
        value: data.value || null,
        status: data.status || "ACTIVE",
      },
    });

    // Format the site setting data for response
    return {
      ...siteSetting,
      id: siteSetting.id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to create site setting: ${error.message}`);
  }
}

/**
 * Update a site setting
 * @param {string} id - Site setting ID
 * @param {Object} data - Site setting data
 * @returns {Object} Updated site setting
 */
async function updateSiteSetting(id, data) {
  try {
    // Validate site setting data
    const { error } = updateSiteSettingsSchema.validate(data);

    if (error) {
      throw new Error(error.details[0].message);
    }

    // Check if site setting exists
    const existingSiteSetting = await prisma.siteSettings.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingSiteSetting) {
      throw new Error("Site setting not found");
    }

    // Check if name already exists (if name is being updated)
    if (data.name && data.name !== existingSiteSetting.name) {
      const existingName = await prisma.siteSettings.findFirst({
        where: {
          name: data.name,
          id: { not: BigInt(id) },
        },
      });

      if (existingName) {
        throw new Error("Setting name already exists");
      }
    }

    // Prepare update data
    const updateData = {};

    // Only update fields that are provided
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined)
      updateData.description = data.description;
    if (data.input_type !== undefined) updateData.input_type = data.input_type;
    if (data.value !== undefined) updateData.value = data.value;
    if (data.status !== undefined) updateData.status = data.status;

    // Update the site setting
    const siteSetting = await prisma.siteSettings.update({
      where: { id: BigInt(id) },
      data: updateData,
    });

    // Format the site setting data for response
    return {
      ...siteSetting,
      id: siteSetting.id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to update site setting: ${error.message}`);
  }
}

/**
 * Update a site setting's status
 * @param {string} id - Site setting ID
 * @param {string} status - New status
 * @returns {Object} Updated site setting
 */
async function updateSiteSettingStatus(id, status) {
  try {
    // Check if site setting exists
    const existingSiteSetting = await prisma.siteSettings.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingSiteSetting) {
      throw new Error("Site setting not found");
    }

    // Update the site setting status
    const siteSetting = await prisma.siteSettings.update({
      where: { id: BigInt(id) },
      data: { status },
    });

    // Format the site setting data for response
    return {
      ...siteSetting,
      id: siteSetting.id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to update site setting status: ${error.message}`);
  }
}

/**
 * Delete a site setting
 * @param {string} id - Site setting ID
 * @returns {Object} Deleted site setting
 */
async function deleteSiteSetting(id) {
  try {
    // Check if site setting exists
    const existingSiteSetting = await prisma.siteSettings.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingSiteSetting) {
      throw new Error("Site setting not found");
    }

    // Delete the site setting
    const siteSetting = await prisma.siteSettings.delete({
      where: { id: BigInt(id) },
    });

    // Format the site setting data for response
    return {
      ...siteSetting,
      id: siteSetting.id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to delete site setting: ${error.message}`);
  }
}

/**
 * Check a site setting status (similar to referral code check)
 * @param {string} name - Site setting name to check
 * @returns {boolean} True if status is ACTIVE, false otherwise
 */
async function checkSiteSetting(name) {
  try {
    // Find the site setting by name
    const siteSetting = await prisma.siteSettings.findFirst({
      where: { name: name },
    });

    // Return true only if site setting exists and status is ACTIVE, false otherwise
    return !!(siteSetting && siteSetting.status === "ACTIVE");
  } catch (error) {
    throw new Error(`Failed to check site setting: ${error.message}`);
  }
}

module.exports = {
  getAllSiteSettings,
  getSiteSettingById,
  getSiteSettingByName,
  createSiteSetting,
  updateSiteSetting,
  updateSiteSettingStatus,
  deleteSiteSetting,
  checkSiteSetting,
};
