const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const { buildPagination } = require("../utils/helpers");

/**
 * Get all notifications with pagination, search, and filtering
 * @param {Object} req - Request object containing query parameters
 * @returns {Object} Notifications with pagination metadata
 */
async function getAllNotifications(req) {
  const {
    page = 1,
    perPage = 10,
    search = "",
    user_id = "",
    is_read = "",
    sortBy = "created_at",
    sortOrder = "desc",
  } = req.query;

  // Build where conditions
  const conditions = [];

  // Search condition
  if (search) {
    conditions.push({
      text: { contains: search },
    });
  }

  // User filter
  if (user_id) {
    if (user_id === "null" || user_id === "global") {
      conditions.push({ user_id: null });
    } else {
      conditions.push({ user_id: BigInt(user_id) });
    }
  }

  // Read status filter
  if (is_read !== "") {
    conditions.push({ is_read: is_read === "true" });
  }

  const whereCondition = {
    AND: conditions.filter((c) => Object.keys(c).length > 0),
  };

  const { skip, take, perPageInt, pageInt } = buildPagination(page, perPage);

  // Valid sort fields
  const validSortFields = ["id", "text", "user_id", "is_read", "created_at", "updated_at"];
  const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
  const sortDirection = sortOrder.toLowerCase() === "asc" ? "asc" : "desc";

  const [notifications, totalCount] = await Promise.all([
    prisma.notification.findMany({
      where: whereCondition,
      orderBy: { [sortField]: sortDirection },
      skip,
      take,
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    }),
    prisma.notification.count({ where: whereCondition }),
  ]);

  const totalPages = Math.ceil(totalCount / perPageInt);

  return {
    data: notifications.map((notification) => ({
      id: notification.id.toString(),
      text: notification.text,
      user_id: notification.user_id ? notification.user_id.toString() : null,
      is_read: notification.is_read,
      created_at: notification.created_at,
      updated_at: notification.updated_at,
      user: notification.user
        ? {
            id: notification.user.id.toString(),
            full_name: notification.user.full_name,
            email: notification.user.email,
          }
        : null,
    })),
    pagination: {
      currentPage: pageInt,
      perPage: perPageInt,
      totalCount,
      totalPages,
      hasNextPage: pageInt < totalPages,
      hasPrevPage: pageInt > 1,
    },
  };
}

/**
 * Get notifications for a specific user (including global notifications)
 * @param {string} userId - User ID
 * @param {Object} query - Query parameters
 * @returns {Object} User notifications with pagination
 */
async function getUserNotifications(userId, query = {}) {
  const {
    page = 1,
    perPage = 10,
    is_read = "",
    sortBy = "created_at",
    sortOrder = "desc",
  } = query;

  // Build where conditions for user-specific and global notifications
  const conditions = [
    { user_id: BigInt(userId) }, // User-specific notifications
    { user_id: null }, // Global notifications
  ];

  // Read status filter
  const whereCondition = {
    OR: conditions,
  };

  if (is_read !== "") {
    whereCondition.is_read = is_read === "true";
  }

  const { skip, take, perPageInt, pageInt } = buildPagination(page, perPage);

  // Valid sort fields
  const validSortFields = ["id", "text", "is_read", "created_at", "updated_at"];
  const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
  const sortDirection = sortOrder.toLowerCase() === "asc" ? "asc" : "desc";

  const [notifications, totalCount] = await Promise.all([
    prisma.notification.findMany({
      where: whereCondition,
      orderBy: { [sortField]: sortDirection },
      skip,
      take,
    }),
    prisma.notification.count({ where: whereCondition }),
  ]);

  const totalPages = Math.ceil(totalCount / perPageInt);

  return {
    data: notifications.map((notification) => ({
      id: notification.id.toString(),
      text: notification.text,
      user_id: notification.user_id ? notification.user_id.toString() : null,
      is_read: notification.is_read,
      is_global: notification.user_id === null,
      created_at: notification.created_at,
      updated_at: notification.updated_at,
    })),
    pagination: {
      currentPage: pageInt,
      perPage: perPageInt,
      totalCount,
      totalPages,
      hasNextPage: pageInt < totalPages,
      hasPrevPage: pageInt > 1,
    },
  };
}

/**
 * Get notification by ID
 * @param {string} id - Notification ID
 * @returns {Object} Notification data
 */
async function getNotificationById(id) {
  const notification = await prisma.notification.findUnique({
    where: { id: BigInt(id) },
    include: {
      user: {
        select: {
          id: true,
          full_name: true,
          email: true,
        },
      },
    },
  });

  if (!notification) {
    throw new Error("Notification not found");
  }

  return {
    id: notification.id.toString(),
    text: notification.text,
    user_id: notification.user_id ? notification.user_id.toString() : null,
    is_read: notification.is_read,
    created_at: notification.created_at,
    updated_at: notification.updated_at,
    user: notification.user
      ? {
          id: notification.user.id.toString(),
          full_name: notification.user.full_name,
          email: notification.user.email,
        }
      : null,
  };
}

/**
 * Create a new notification
 * @param {Object} data - Notification data
 * @returns {Object} Created notification
 */
async function createNotification(data) {
  // Validate user exists if user_id is provided
  if (data.user_id) {
    const user = await prisma.user.findUnique({
      where: { id: BigInt(data.user_id) },
    });

    if (!user) {
      throw new Error("User not found");
    }
  }

  const notification = await prisma.notification.create({
    data: {
      text: data.text,
      user_id: data.user_id ? BigInt(data.user_id) : null,
      is_read: data.is_read || false,
    },
    include: {
      user: {
        select: {
          id: true,
          full_name: true,
          email: true,
        },
      },
    },
  });

  return {
    id: notification.id.toString(),
    text: notification.text,
    user_id: notification.user_id ? notification.user_id.toString() : null,
    is_read: notification.is_read,
    created_at: notification.created_at,
    updated_at: notification.updated_at,
    user: notification.user
      ? {
          id: notification.user.id.toString(),
          full_name: notification.user.full_name,
          email: notification.user.email,
        }
      : null,
  };
}

/**
 * Update notification
 * @param {string} id - Notification ID
 * @param {Object} data - Updated notification data
 * @returns {Object} Updated notification
 */
async function updateNotification(id, data) {
  // Check if notification exists
  const existingNotification = await prisma.notification.findUnique({
    where: { id: BigInt(id) },
  });

  if (!existingNotification) {
    throw new Error("Notification not found");
  }

  // Validate user exists if user_id is provided
  if (data.user_id !== undefined && data.user_id !== null) {
    const user = await prisma.user.findUnique({
      where: { id: BigInt(data.user_id) },
    });

    if (!user) {
      throw new Error("User not found");
    }
  }

  const notification = await prisma.notification.update({
    where: { id: BigInt(id) },
    data: {
      ...(data.text && { text: data.text }),
      ...(data.user_id !== undefined && {
        user_id: data.user_id ? BigInt(data.user_id) : null
      }),
      ...(data.is_read !== undefined && { is_read: data.is_read }),
    },
    include: {
      user: {
        select: {
          id: true,
          full_name: true,
          email: true,
        },
      },
    },
  });

  return {
    id: notification.id.toString(),
    text: notification.text,
    user_id: notification.user_id ? notification.user_id.toString() : null,
    is_read: notification.is_read,
    created_at: notification.created_at,
    updated_at: notification.updated_at,
    user: notification.user
      ? {
          id: notification.user.id.toString(),
          full_name: notification.user.full_name,
          email: notification.user.email,
        }
      : null,
  };
}

/**
 * Mark notification as read/unread
 * @param {string} id - Notification ID
 * @param {boolean} isRead - Read status
 * @returns {Object} Updated notification
 */
async function markAsRead(id, isRead) {
  // Check if notification exists
  const existingNotification = await prisma.notification.findUnique({
    where: { id: BigInt(id) },
  });

  if (!existingNotification) {
    throw new Error("Notification not found");
  }

  const notification = await prisma.notification.update({
    where: { id: BigInt(id) },
    data: { is_read: isRead },
    include: {
      user: {
        select: {
          id: true,
          full_name: true,
          email: true,
        },
      },
    },
  });

  return {
    id: notification.id.toString(),
    text: notification.text,
    user_id: notification.user_id ? notification.user_id.toString() : null,
    is_read: notification.is_read,
    created_at: notification.created_at,
    updated_at: notification.updated_at,
    user: notification.user
      ? {
          id: notification.user.id.toString(),
          full_name: notification.user.full_name,
          email: notification.user.email,
        }
      : null,
  };
}

/**
 * Bulk mark notifications as read/unread
 * @param {Array} notificationIds - Array of notification IDs
 * @param {boolean} isRead - Read status
 * @returns {Object} Update result
 */
async function bulkMarkAsRead(notificationIds, isRead = true) {
  const ids = notificationIds.map(id => BigInt(id));

  const result = await prisma.notification.updateMany({
    where: {
      id: { in: ids },
    },
    data: { is_read: isRead },
  });

  return {
    updated_count: result.count,
    is_read: isRead,
  };
}

/**
 * Delete notification
 * @param {string} id - Notification ID
 * @returns {Object} Deleted notification
 */
async function deleteNotification(id) {
  // Check if notification exists
  const existingNotification = await prisma.notification.findUnique({
    where: { id: BigInt(id) },
    include: {
      user: {
        select: {
          id: true,
          full_name: true,
          email: true,
        },
      },
    },
  });

  if (!existingNotification) {
    throw new Error("Notification not found");
  }

  await prisma.notification.delete({
    where: { id: BigInt(id) },
  });

  return {
    id: existingNotification.id.toString(),
    text: existingNotification.text,
    user_id: existingNotification.user_id ? existingNotification.user_id.toString() : null,
    is_read: existingNotification.is_read,
    created_at: existingNotification.created_at,
    updated_at: existingNotification.updated_at,
    user: existingNotification.user
      ? {
          id: existingNotification.user.id.toString(),
          full_name: existingNotification.user.full_name,
          email: existingNotification.user.email,
        }
      : null,
  };
}

/**
 * Get unread notification count for a user
 * @param {string} userId - User ID
 * @returns {Object} Unread count
 */
async function getUnreadCount(userId) {
  const count = await prisma.notification.count({
    where: {
      OR: [
        { user_id: BigInt(userId) },
        { user_id: null },
      ],
      is_read: false,
    },
  });

  return { unread_count: count };
}

module.exports = {
  getAllNotifications,
  getUserNotifications,
  getNotificationById,
  createNotification,
  updateNotification,
  markAsRead,
  bulkMarkAsRead,
  deleteNotification,
  getUnreadCount,
};
