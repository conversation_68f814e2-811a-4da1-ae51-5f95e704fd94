const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const { buildPagination } = require("../utils/helpers");

/**
 * Get all testimonials with pagination, search, and filtering
 * @param {Object} req - Request object containing query parameters
 * @returns {Object} Testimonials with pagination metadata
 */
async function getAllTestimonials(req) {
  const {
    page = 1,
    perPage = 10,
    search = "",
    status = "",
    sortBy = "created_at",
    sortOrder = "desc",
  } = req.query;

  // Build where conditions
  const conditions = [];

  // Search condition
  if (search) {
    conditions.push({
      OR: [
        { name: { contains: search } },
        { designation: { contains: search } },
        { description: { contains: search } },
      ],
    });
  }

  // Status filter
  if (status) {
    conditions.push({ status });
  }

  const whereCondition = {
    AND: conditions.filter((c) => Object.keys(c).length > 0),
  };

  const { skip, take, perPageInt, pageInt } = buildPagination(page, perPage);

  // Valid sort fields
  const validSortFields = ["id", "name", "designation", "status", "created_at", "updated_at"];
  const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
  const sortDirection = sortOrder.toLowerCase() === "asc" ? "asc" : "desc";

  const [testimonials, totalCount] = await Promise.all([
    prisma.testimonial.findMany({
      where: whereCondition,
      orderBy: { [sortField]: sortDirection },
      skip,
      take,
    }),
    prisma.testimonial.count({ where: whereCondition }),
  ]);

  const totalPages = Math.ceil(totalCount / perPageInt);

  return {
    data: testimonials.map((testimonial) => ({
      id: testimonial.id.toString(),
      name: testimonial.name,
      designation: testimonial.designation,
      description: testimonial.description,
      photo: testimonial.photo,
      bg_color: testimonial.bg_color,
      status: testimonial.status,
      created_at: testimonial.created_at,
      updated_at: testimonial.updated_at,
    })),
    pagination: {
      currentPage: pageInt,
      perPage: perPageInt,
      totalCount,
      totalPages,
      hasNextPage: pageInt < totalPages,
      hasPrevPage: pageInt > 1,
    },
  };
}

/**
 * Get testimonial by ID
 * @param {string} id - Testimonial ID
 * @returns {Object} Testimonial data
 */
async function getTestimonialById(id) {
  const testimonial = await prisma.testimonial.findUnique({
    where: { id: BigInt(id) },
  });

  if (!testimonial) {
    throw new Error("Testimonial not found");
  }

  return {
    id: testimonial.id.toString(),
    name: testimonial.name,
    designation: testimonial.designation,
    description: testimonial.description,
    photo: testimonial.photo,
    bg_color: testimonial.bg_color,
    status: testimonial.status,
    created_at: testimonial.created_at,
    updated_at: testimonial.updated_at,
  };
}

/**
 * Create a new testimonial
 * @param {Object} data - Testimonial data
 * @returns {Object} Created testimonial
 */
async function createTestimonial(data) {
  const testimonial = await prisma.testimonial.create({
    data: {
      name: data.name,
      designation: data.designation,
      description: data.description || null,
      photo: data.photo || null,
      bg_color: data.bg_color || null,
      status: data.status || "ACTIVE",
    },
  });

  return {
    id: testimonial.id.toString(),
    name: testimonial.name,
    designation: testimonial.designation,
    description: testimonial.description,
    photo: testimonial.photo,
    bg_color: testimonial.bg_color,
    status: testimonial.status,
    created_at: testimonial.created_at,
    updated_at: testimonial.updated_at,
  };
}

/**
 * Update testimonial
 * @param {string} id - Testimonial ID
 * @param {Object} data - Updated testimonial data
 * @returns {Object} Updated testimonial
 */
async function updateTestimonial(id, data) {
  // Check if testimonial exists
  const existingTestimonial = await prisma.testimonial.findUnique({
    where: { id: BigInt(id) },
  });

  if (!existingTestimonial) {
    throw new Error("Testimonial not found");
  }

  const testimonial = await prisma.testimonial.update({
    where: { id: BigInt(id) },
    data: {
      ...(data.name && { name: data.name }),
      ...(data.designation && { designation: data.designation }),
      ...(data.description !== undefined && { description: data.description }),
      ...(data.photo !== undefined && { photo: data.photo }),
      ...(data.bg_color !== undefined && { bg_color: data.bg_color }),
      ...(data.status && { status: data.status }),
    },
  });

  return {
    id: testimonial.id.toString(),
    name: testimonial.name,
    designation: testimonial.designation,
    description: testimonial.description,
    photo: testimonial.photo,
    bg_color: testimonial.bg_color,
    status: testimonial.status,
    created_at: testimonial.created_at,
    updated_at: testimonial.updated_at,
  };
}

/**
 * Update testimonial status
 * @param {string} id - Testimonial ID
 * @param {string} status - New status
 * @returns {Object} Updated testimonial
 */
async function updateTestimonialStatus(id, status) {
  // Check if testimonial exists
  const existingTestimonial = await prisma.testimonial.findUnique({
    where: { id: BigInt(id) },
  });

  if (!existingTestimonial) {
    throw new Error("Testimonial not found");
  }

  const testimonial = await prisma.testimonial.update({
    where: { id: BigInt(id) },
    data: { status },
  });

  return {
    id: testimonial.id.toString(),
    name: testimonial.name,
    designation: testimonial.designation,
    description: testimonial.description,
    photo: testimonial.photo,
    bg_color: testimonial.bg_color,
    status: testimonial.status,
    created_at: testimonial.created_at,
    updated_at: testimonial.updated_at,
  };
}

/**
 * Delete testimonial
 * @param {string} id - Testimonial ID
 * @returns {Object} Deleted testimonial
 */
async function deleteTestimonial(id) {
  // Check if testimonial exists
  const existingTestimonial = await prisma.testimonial.findUnique({
    where: { id: BigInt(id) },
  });

  if (!existingTestimonial) {
    throw new Error("Testimonial not found");
  }

  const testimonial = await prisma.testimonial.delete({
    where: { id: BigInt(id) },
  });

  return {
    id: testimonial.id.toString(),
    name: testimonial.name,
    designation: testimonial.designation,
    description: testimonial.description,
    photo: testimonial.photo,
    bg_color: testimonial.bg_color,
    status: testimonial.status,
    created_at: testimonial.created_at,
    updated_at: testimonial.updated_at,
  };
}

/**
 * Get active testimonials for public display
 * @returns {Array} Active testimonials
 */
async function getActiveTestimonials() {
  const testimonials = await prisma.testimonial.findMany({
    where: { status: "ACTIVE" },
    orderBy: { created_at: "desc" },
  });

  return testimonials.map((testimonial) => ({
    id: testimonial.id.toString(),
    name: testimonial.name,
    designation: testimonial.designation,
    description: testimonial.description,
    photo: testimonial.photo,
    bg_color: testimonial.bg_color,
    created_at: testimonial.created_at,
  }));
}

module.exports = {
  getAllTestimonials,
  getTestimonialById,
  createTestimonial,
  updateTestimonial,
  updateTestimonialStatus,
  deleteTestimonial,
  getActiveTestimonials,
};
