const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const {
  buildSearchCondition,
  buildPagination,
  generatePaginationMetadata,
  normalizeQuery,
} = require("../utils/helpers");
const {
  createReferralCodeSchema,
  updateReferralCodeSchema,
} = require("../validations/referralCodeValidation");

/**
 * Get all referral codes with pagination, search, and filter
 * @param {Object} req - Request object
 * @returns {Object} Referral codes with pagination metadata
 */
async function getAllReferralCodes(req) {
  try {
    const {
      page = 1,
      perPage = 10,
      search,
      status,
      sort,
      order,
      user_id,
    } = normalizeQuery(req.query);
    const pageInt = parseInt(page, 10);
    const perPageInt = parseInt(perPage, 10);

    // Build search condition
    const searchCondition = buildSearchCondition(search, ["code"]);

    // Build status filter condition
    const statusCondition = status ? { status } : {};

    // Build user filter
    const userFilter = user_id ? { user_id: BigInt(user_id) } : {};


    // Combine conditions
    const whereCondition = {
      AND: [searchCondition, statusCondition, userFilter],
    };

    // Count total records
    const totalCount = await prisma.referralCode.count({
      where: whereCondition,
    });

    // Build pagination
    const { skip, take } = buildPagination(pageInt, perPageInt);

    // Build sort order
    const orderBy = {};
    if (sort && order) {
      orderBy[sort] = order.toLowerCase();
    } else {
      // Default sorting
      orderBy.id = "desc";
    }

    // Get referral codes with pagination
    const referralCodes = await prisma.referralCode.findMany({
      where: whereCondition,
      skip,
      take,
      orderBy,
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Format referral codes for response
    const formattedReferralCodes = referralCodes.map((referralCode) => ({
      ...referralCode,
      id: referralCode.id.toString(),
      user_id: referralCode.user_id ? referralCode.user_id.toString() : null,
      user: referralCode.user
        ? {
            ...referralCode.user,
            id: referralCode.user.id.toString(),
          }
        : null,
    }));

    // Generate pagination metadata
    const pagination = generatePaginationMetadata(
      req,
      pageInt,
      totalCount,
      perPageInt
    );

    return {
      ...pagination,
      data: formattedReferralCodes,
    };
  } catch (error) {
    throw new Error(`Failed to retrieve invitation codes: ${error.message}`);
  }
}

/**
 * Get a referral code by ID
 * @param {string} id - Referral code ID
 * @returns {Object} Referral code
 */
async function getReferralCodeById(id) {
  try {
    const referralCode = await prisma.referralCode.findUnique({
      where: { id: BigInt(id) },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    if (!referralCode) {
      throw new Error("invitation code not found");
    }

    // Format referral code for response
    return {
      ...referralCode,
      id: referralCode.id.toString(),
      user_id: referralCode.user_id ? referralCode.user_id.toString() : null,
      user: referralCode.user
        ? {
            ...referralCode.user,
            id: referralCode.user.id.toString(),
          }
        : null,
    };
  } catch (error) {
    throw new Error(`Failed to retrieve invitation code: ${error.message}`);
  }
}

/**
 * Create a new referral code
 * @param {Object} data - Referral code data
 * @returns {Object} Created referral code
 */
async function createReferralCode(data) {
  try {
    // Validate referral code data
    const { error } = createReferralCodeSchema.validate(data);

    if (error) {
      throw new Error(error.details[0].message);
    }

    // Check if referral code already exists
    const existingCode = await prisma.referralCode.findFirst({
      where: { code: data.code },
    });

    if (existingCode) {
      throw new Error("invitation code already exists");
    }

    // Check if user exists if user_id is provided
    if (data.user_id) {
      const user = await prisma.user.findUnique({
        where: { id: BigInt(data.user_id) },
      });

      if (!user) {
        throw new Error("User not found");
      }
    }

    // Create the referral code
    const referralCode = await prisma.referralCode.create({
      data: {
        code: data.code,
        user_id: data.user_id ? BigInt(data.user_id) : null,
        status: data.status || "ACTIVE",
      },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Format the referral code data for response
    return {
      ...referralCode,
      id: referralCode.id.toString(),
      user_id: referralCode.user_id ? referralCode.user_id.toString() : null,
      user: referralCode.user
        ? {
            ...referralCode.user,
            id: referralCode.user.id.toString(),
          }
        : null,
    };
  } catch (error) {
    throw new Error(`Failed to create invitation code: ${error.message}`);
  }
}

/**
 * Update a referral code
 * @param {string} id - Referral code ID
 * @param {Object} data - Referral code data
 * @returns {Object} Updated referral code
 */
async function updateReferralCode(id, data) {
  try {
    // Validate referral code data
    const { error } = updateReferralCodeSchema.validate(data);

    if (error) {
      throw new Error(error.details[0].message);
    }

    // Check if referral code exists
    const existingReferralCode = await prisma.referralCode.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingReferralCode) {
      throw new Error("Referral code not found");
    }

    // Check if code already exists (if code is being updated)
    if (data.code && data.code !== existingReferralCode.code) {
      const existingCode = await prisma.referralCode.findFirst({
        where: {
          code: data.code,
          id: { not: BigInt(id) },
        },
      });

      if (existingCode) {
        throw new Error("invitation code already exists");
      }
    }

    // Check if user exists if user_id is provided
    if (data.user_id) {
      const user = await prisma.user.findUnique({
        where: { id: BigInt(data.user_id) },
      });

      if (!user) {
        throw new Error("User not found");
      }
    }

    // Prepare update data
    const updateData = {};

    // Only update fields that are provided
    if (data.code !== undefined) updateData.code = data.code;
    if (data.user_id !== undefined)
      updateData.user_id = data.user_id ? BigInt(data.user_id) : null;
    if (data.status !== undefined) updateData.status = data.status;

    // Update the referral code
    const referralCode = await prisma.referralCode.update({
      where: { id: BigInt(id) },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Format the referral code data for response
    return {
      ...referralCode,
      id: referralCode.id.toString(),
      user_id: referralCode.user_id ? referralCode.user_id.toString() : null,
      user: referralCode.user
        ? {
            ...referralCode.user,
            id: referralCode.user.id.toString(),
          }
        : null,
    };
  } catch (error) {
    throw new Error(`Failed to update invitation code: ${error.message}`);
  }
}

/**
 * Update a referral code's status
 * @param {string} id - Referral code ID
 * @param {string} status - New status
 * @returns {Object} Updated referral code
 */
async function updateReferralCodeStatus(id, status) {
  try {
    // Check if referral code exists
    const existingReferralCode = await prisma.referralCode.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingReferralCode) {
      throw new Error("invitation code not found");
    }

    // Update the referral code status
    const referralCode = await prisma.referralCode.update({
      where: { id: BigInt(id) },
      data: { status },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Format the referral code data for response
    return {
      ...referralCode,
      id: referralCode.id.toString(),
      user_id: referralCode.user_id ? referralCode.user_id.toString() : null,
      user: referralCode.user
        ? {
            ...referralCode.user,
            id: referralCode.user.id.toString(),
          }
        : null,
    };
  } catch (error) {
    throw new Error(`Failed to update referral code status: ${error.message}`);
  }
}

/**
 * Delete a referral code
 * @param {string} id - Referral code ID
 * @returns {Object} Deleted referral code
 */
async function deleteReferralCode(id) {
  try {
    // Check if referral code exists
    const existingReferralCode = await prisma.referralCode.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingReferralCode) {
      throw new Error("Referral code not found");
    }

    // Delete the referral code
    const referralCode = await prisma.referralCode.delete({
      where: { id: BigInt(id) },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Format the referral code data for response
    return {
      ...referralCode,
      id: referralCode.id.toString(),
      user_id: referralCode.user_id ? referralCode.user_id.toString() : null,
      user: referralCode.user
        ? {
            ...referralCode.user,
            id: referralCode.user.id.toString(),
          }
        : null,
    };
  } catch (error) {
    throw new Error(`Failed to delete referral code: ${error.message}`);
  }
}

/**
 * Validate a referral code
 * @param {string} code - Referral code to validate
 * @returns {Object} Validation result
 */
async function validateReferralCode(code) {
  try {
    // Find the referral code by code string
    const referralCode = await prisma.referralCode.findFirst({
      where: { code: code },
    });

    // If referral code doesn't exist
    if (!referralCode) {
      return {
        valid: false,
        code: code,
        message: "Code not found",
      };
    }

    // Check if the referral code status is ACTIVE
    if (referralCode.status === "ACTIVE") {
      return {
        valid: true,
        code: code,
        message: "Code is valid",
      };
    } else {
      return {
        valid: false,
        code: code,
        message: `Code is invalid`,
      };
    }
  } catch (error) {
    throw new Error(`Failed to validate code: ${error.message}`);
  }
}

/**
 * Check a referral code status (similar to site-settings check)
 * @param {string} value - Referral code value to check
 * @returns {boolean} True if status is ACTIVE, false otherwise
 */
async function checkReferralCode(value) {
  try {
    // Find the referral code by code string
    const referralCode = await prisma.referralCode.findFirst({
      where: { code: value },
    });

    // Return true only if referral code exists and status is ACTIVE, false otherwise
    return !!(referralCode && referralCode.status === "ACTIVE");
  } catch (error) {
    throw new Error(`Failed to check referral code: ${error.message}`);
  }
}

module.exports = {
  getAllReferralCodes,
  getReferralCodeById,
  createReferralCode,
  updateReferralCode,
  updateReferralCodeStatus,
  deleteReferralCode,
  validateReferralCode,
  checkReferralCode,
};
