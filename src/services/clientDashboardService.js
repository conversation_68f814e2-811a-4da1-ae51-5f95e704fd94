const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const Auth = require("../utils/auth");
const { TRANSACTION_STATUS } = require("../constants/constant");

/**
 * Get balance changes data for the last 7 days for the authenticated user
 * @param {Object} req - Request object
 * @returns {Object} Balance changes data for chart
 */
async function getBalanceChangesData(req) {
  try {
    const userId = await Auth.id(req);
    if (!userId) throw new Error("User not authenticated");

    // Calculate date range for the last 7 days
    const today = new Date();
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 6);
    sevenDaysAgo.setHours(0, 0, 0, 0);

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, balance: true },
    });

    if (!user) throw new Error("User not found");

    // Get all balance records for the user within the date range
    // We need to get records from before the range too for forward-filling
    const extendedStartDate = new Date(sevenDaysAgo);
    extendedStartDate.setDate(sevenDaysAgo.getDate() - 30); // Look back 30 days to find previous balance

    const balanceRecords = await prisma.balance.findMany({
      where: {
        user_id: userId,
        created_at: { gte: extendedStartDate },
      },
      orderBy: { created_at: "desc" },
      select: {
        after_balance: true,
        created_at: true,
      },
    });

    // Create array for the 7 days we need
    const dailyData = [];
    let lastKnownBalance = null;

    // Process each day from oldest to newest
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(sevenDaysAgo);
      currentDate.setDate(sevenDaysAgo.getDate() + i);

      const startOfDay = new Date(currentDate);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(currentDate);
      endOfDay.setHours(23, 59, 59, 999);

      // Find the most recent balance record for this day or before
      let dayBalance = null;

      // First, check if there's a balance record on this specific day
      const dayRecords = balanceRecords.filter((record) => {
        const recordDate = new Date(record.created_at);
        return recordDate >= startOfDay && recordDate <= endOfDay;
      });

      if (dayRecords.length > 0) {
        // Use the latest balance from this day
        dayBalance = parseFloat(dayRecords[0].after_balance);
        lastKnownBalance = dayBalance;
      } else {
        // No record for this day, find the most recent record before this day
        const previousRecords = balanceRecords.filter((record) => {
          const recordDate = new Date(record.created_at);
          return recordDate < startOfDay;
        });

        if (previousRecords.length > 0) {
          dayBalance = parseFloat(previousRecords[0].after_balance);
          lastKnownBalance = dayBalance;
        } else if (lastKnownBalance !== null) {
          // Forward-fill from the last known balance
          dayBalance = lastKnownBalance;
        } else {
          // No previous balance found, use current user balance or 0
          dayBalance = parseFloat(user.balance) || 0;
          lastKnownBalance = dayBalance;
        }
      }

      // Format the date as "MMM DD, YYYY"
      const formattedDate = currentDate.toLocaleDateString("en-US", {
        month: "short",
        day: "2-digit",
        year: "numeric",
      });

      dailyData.push({
        date: formattedDate,
        balance: dayBalance.toFixed(2),
      });
    }

    return { data: dailyData };
  } catch (error) {
    throw new Error(`Failed to fetch balance changes data: ${error.message}`);
  }
}

module.exports = {
  getBalanceChangesData,
};
