const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const Auth = require("../utils/auth");
const { TRANSACTION_STATUS } = require("../constants/constant");

/**
 * Get balance changes data for the last 7 days for the authenticated user
 * @param {Object} req - Request object
 * @returns {Object} Balance changes data for chart
 */
async function getBalanceChangesData(req) {
  try {
    const userId = await Auth.id(req);
    if (!userId) throw new Error("User not authenticated");

    const today = new Date();
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 6);
    sevenDaysAgo.setHours(0, 0, 0, 0);

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { balance: true },
    });

    if (!user) throw new Error("User not found");

    const transactions = await prisma.balance.findMany({
      where: {
        user_id: userId,
        created_at: { gte: sevenDaysAgo },
        // status: TRANSACTION_STATUS.COMPLETED,
      },
      orderBy: { created_at: "asc" },
      select: {
        amount: true,
        created_at: true,
      },
    });

    // return transactions;

    const dailyData = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(sevenDaysAgo);
      date.setDate(sevenDaysAgo.getDate() + i);

      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      const dayTransactions = transactions.filter((tx) => {
        const txDate = new Date(tx.created_at);
        return txDate >= startOfDay && txDate <= endOfDay;
      });

      const totalAmount = dayTransactions.reduce(
        (sum, tx) => sum + parseFloat(tx.amount),
        0
      );

      const formattedDate = date.toLocaleDateString("en-US", {
        month: "short",
        day: "2-digit",
        year: "numeric",
      });

      dailyData.push({
        date: formattedDate,
        balance: totalAmount.toFixed(2),
      });
    }

    return dailyData;
  } catch (error) {
    throw new Error(`Failed to fetch balance changes data: ${error.message}`);
  }
}

module.exports = {
  getBalanceChangesData,
};
