const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const {
  createBlogSchema,
  updateBlogSchema,
} = require("../validations/blogValidation");
const {
  buildSearchCondition,
  buildFilterCondition,
  buildPagination,
  generatePaginationMetadata,
  normalizeQuery,
} = require("../utils/helpers");

/**
 * Get all blogs with pagination, search, and filtering
 * @param {Object} req - Request object
 * @returns {Object} Blogs with pagination metadata
 */
async function getAllBlogs(req) {
  try {
    const query = normalizeQuery(req.query);
    const { search, status, page = 1, per_page = 10 } = query;

    // Define searchable fields
    const searchableFields = ["title", "description"];

    // Build search condition
    const searchCondition = buildSearchCondition(search, searchableFields);

    // Build filter condition
    const filterCondition = buildFilterCondition({ status });

    // Combine conditions
    const whereCondition = {
      ...searchCondition,
      ...filterCondition,
    };

    // Build pagination
    const { skip, take, perPageInt, pageInt } = buildPagination(page, per_page);

    // Get total count
    const totalCount = await prisma.blog.count({
      where: whereCondition,
    });

    // Get blogs with pagination
    const blogs = await prisma.blog.findMany({
      where: whereCondition,
      skip,
      take,
      orderBy: {
        created_at: "desc",
      },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Generate pagination metadata
    const paginationMeta = generatePaginationMetadata(
      req,
      pageInt,
      totalCount,
      perPageInt
    );

    // Format the response
    return {
      data: blogs.map((blog) => ({
        ...blog,
        id: blog.id.toString(),
        user_id: blog.user_id.toString(),
      })),
      meta: paginationMeta,
    };
  } catch (error) {
    throw new Error(`Failed to fetch blogs: ${error.message}`);
  }
}

/**
 * Get blog by ID
 * @param {string|number} id - Blog ID
 * @returns {Object} Blog
 */
async function getBlogById(id) {
  try {
    const blog = await prisma.blog.findUnique({
      where: { id: BigInt(id) },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    if (!blog) {
      throw new Error(`Blog with ID ${id} not found`);
    }

    // Format the blog data
    return {
      ...blog,
      id: blog.id.toString(),
      user_id: blog.user_id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to fetch blog: ${error.message}`);
  }
}

/**
 * Create new blog
 * @param {Object} data - Blog data
 * @param {Object} req - Request object with user information
 * @returns {Object} Created blog
 */
async function createBlog(data, req) {
  try {
    // Validate blog data
    const { error } = createBlogSchema.validate(data);

    if (error) {
      throw new Error(error.details[0].message);
    }

    // Get user ID from authenticated user
    const userId = req.user.id;

    // Create the blog
    const blog = await prisma.blog.create({
      data: {
        title: data.title,
        description: data.description || null,
        image: data.image || null,
        status: data.status || "ACTIVE",
        user_id: BigInt(userId),
      },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Format the blog data
    return {
      ...blog,
      id: blog.id.toString(),
      user_id: blog.user_id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to create blog: ${error.message}`);
  }
}

/**
 * Update blog
 * @param {string|number} id - Blog ID
 * @param {Object} data - Blog data
 * @returns {Object} Updated blog
 */
async function updateBlog(id, data) {
  try {
    // Validate blog data
    const { error } = updateBlogSchema.validate(data);

    if (error) {
      throw new Error(error.details[0].message);
    }

    // Check if blog exists
    const existingBlog = await prisma.blog.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingBlog) {
      throw new Error(`Blog with ID ${id} not found`);
    }

    // Prepare update data
    const updateData = {};

    // Only update fields that are provided
    if (data.title !== undefined) updateData.title = data.title;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.image !== undefined) updateData.image = data.image;
    if (data.status !== undefined) updateData.status = data.status;

    // Update the blog
    const blog = await prisma.blog.update({
      where: { id: BigInt(id) },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Format the blog data
    return {
      ...blog,
      id: blog.id.toString(),
      user_id: blog.user_id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to update blog: ${error.message}`);
  }
}

/**
 * Update blog status
 * @param {string|number} id - Blog ID
 * @param {string} status - New status
 * @returns {Object} Updated blog
 */
async function updateBlogStatus(id, status) {
  try {
    // Check if blog exists
    const existingBlog = await prisma.blog.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingBlog) {
      throw new Error(`Blog with ID ${id} not found`);
    }

    // Update the blog status
    const blog = await prisma.blog.update({
      where: { id: BigInt(id) },
      data: { status },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Format the blog data
    return {
      ...blog,
      id: blog.id.toString(),
      user_id: blog.user_id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to update blog status: ${error.message}`);
  }
}

/**
 * Delete blog
 * @param {string|number} id - Blog ID
 * @returns {Object} Deleted blog
 */
async function deleteBlog(id) {
  try {
    // Check if blog exists
    const existingBlog = await prisma.blog.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingBlog) {
      throw new Error(`Blog with ID ${id} not found`);
    }

    // Delete the blog
    const blog = await prisma.blog.delete({
      where: { id: BigInt(id) },
    });

    // Format the blog data
    return {
      ...blog,
      id: blog.id.toString(),
      user_id: blog.user_id.toString(),
    };
  } catch (error) {
    throw new Error(`Failed to delete blog: ${error.message}`);
  }
}

module.exports = {
  getAllBlogs,
  getBlogById,
  createBlog,
  updateBlog,
  updateBlogStatus,
  deleteBlog,
};
