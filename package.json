{"name": "crypto", "version": "0.0.0", "private": true, "scripts": {"dev": "nodemon ./bin/www", "start": "node ./bin/www", "prisma:merge": "node prisma/merge-prisma.js", "prisma:generate": "npm run prisma:merge && prisma generate", "seed": "node prisma/seed.js", "seed:currencies": "node prisma/seed.js currencies", "seed:networks": "node prisma/seed.js networks", "seed:countries": "node prisma/seed.js countries", "seed:users": "node prisma/seed.js users", "seed:banks": "node prisma/seed.js banks", "seed:usernetworks": "node prisma/seed.js usernetworks", "seed:transactionfees": "node prisma/seed.js transactionfees", "seed:balances": "node prisma/seed.js balances"}, "dependencies": {"@prisma/client": "^6.5.0", "bcryptjs": "^3.0.2", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "debug": "~2.6.9", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "~5.0.1", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "http-errors": "~1.6.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "morgan": "~1.9.1", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.0", "nodemailer": "^6.10.0", "npm": "^11.2.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@types/node": "^22.13.13", "nodemon": "^3.1.7", "prisma": "^6.5.0", "prisma-merge": "^0.2.0", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.2", "uuid": "^11.1.0"}, "prisma": {"seed": "node prisma/seed.js"}}