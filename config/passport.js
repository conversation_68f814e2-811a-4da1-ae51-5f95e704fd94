const passport = require("passport");
const GoogleStrategy = require("passport-google-oauth20").Strategy;
const prisma = require("./prisma");
const jwt = require("jsonwebtoken");

const JWT_SECRET = process.env.JWT_SECRET || "your_secret_key";

// Google OAuth Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL:
        process.env.GOOGLE_CALLBACK_URL ||
        "http://localhost:3000/api/auth/google/callback",
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        // Check if user already exists with this Google ID
        let user = await prisma.user.findFirst({
          where: { google_id: profile.id },
        });

        if (user) {
          // User exists, return user
          return done(null, user);
        }

        // Check if user exists with the same email
        user = await prisma.user.findUnique({
          where: { email: profile.emails[0].value },
        });

        if (user) {
          // User exists with same email, link Google account
          user = await prisma.user.update({
            where: { id: user.id },
            data: { google_id: profile.id },
          });
          return done(null, user);
        }

        // Create new user
        const userCode = `USR-${Date.now().toString().slice(-6)}${Math.floor(
          Math.random() * 1000
        )}`;

        user = await prisma.user.create({
          data: {
            full_name: profile.displayName,
            email: profile.emails[0].value,
            google_id: profile.id,
            code: userCode,
            // Set a random password for Google users (they won't use it)
            password: await require("bcryptjs").hash(
              Math.random().toString(36),
              10
            ),
            // Leave country_id as null for Google users - they can set it later
            // country_id: BigInt(1),
          },
        });

        return done(null, user);
      } catch (error) {
        console.error("Google OAuth error:", error);
        return done(error, null);
      }
    }
  )
);

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: id },
      select: {
        id: true,
        full_name: true,
        email: true,
        role: true,
        status: true,
        created_at: true,
        updated_at: true,
      },
    });
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

module.exports = passport;
